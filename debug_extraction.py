#!/usr/bin/env python3
"""
Debug the Cognizant extraction issue
"""

import asyncio
import json
from playwright.async_api import async_playwright

async def debug_cognizant_extraction():
    """Debug the exact extraction process"""
    
    url = "https://careers.cognizant.com/global-en/jobs/00060638381/senior-java-developer/"
    
    # The selectors from your input.csv
    selectors = {
        "title": ".text-wrapper .hero-heading",
        "job_description": "div.col-lg-8.main-col", 
        "skills": "ul",
        "location": "not_found",
        "apply_link": "a.js-apply-now.btn.btn-primary"
    }
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            print(f"🔍 Debugging extraction for: {url}")
            await page.goto(url, timeout=30000)
            
            # Wait for page to load
            try:
                await page.wait_for_load_state('domcontentloaded', timeout=10000)
                print("✅ DOM content loaded")
            except:
                print("⚠️ DOM load timeout")

            # Additional wait for dynamic content
            try:
                await page.wait_for_load_state('networkidle', timeout=10000)
                print("✅ Network idle")
            except:
                print("⚠️ Network idle timeout")

            # Wait a bit more for any JavaScript to execute
            await page.wait_for_timeout(3000)
            print("✅ Additional 3s wait completed")

            # Check page title and basic structure
            title = await page.title()
            print(f"📄 Page title: {title}")

            # Check if basic elements exist
            body_content = await page.query_selector('body')
            if body_content:
                body_text = await body_content.text_content()
                print(f"📄 Body content length: {len(body_text) if body_text else 0} characters")
            else:
                print("❌ No body element found!")

            # Check for any h1 elements
            h1_elements = await page.query_selector_all('h1')
            print(f"📄 Found {len(h1_elements)} h1 elements")
            if h1_elements:
                for i, h1 in enumerate(h1_elements[:3]):
                    h1_text = await h1.text_content()
                    print(f"   H1 {i+1}: {h1_text[:100] if h1_text else 'No text'}")

            # Check for any elements with 'hero' in class
            hero_elements = await page.query_selector_all('[class*="hero"]')
            print(f"📄 Found {len(hero_elements)} elements with 'hero' in class")
            if hero_elements:
                for i, elem in enumerate(hero_elements[:3]):
                    elem_text = await elem.text_content()
                    elem_class = await elem.get_attribute('class')
                    print(f"   Hero {i+1} (class: {elem_class}): {elem_text[:100] if elem_text else 'No text'}")
            
            # Simulate the exact _safe_extract logic
            print("\n🧪 Testing _safe_extract logic:")
            print("=" * 50)
            
            for field, selector in selectors.items():
                if selector == "not_found":
                    print(f"❌ {field}: Skipped (marked as not_found)")
                    continue
                    
                print(f"\n🔍 Testing {field}: {selector}")
                
                # Exact _safe_extract logic
                if not selector:
                    print(f"   ❌ Selector is None/empty")
                    continue
                
                try:
                    elements = await page.query_selector_all(selector)
                    print(f"   📊 Found {len(elements)} elements")
                    
                    if not elements:
                        print(f"   ❌ No elements found")
                        continue
                    
                    contents = []
                    for i, element in enumerate(elements):
                        content = await element.text_content()
                        print(f"   📝 Element {i+1} text_content: {repr(content[:100]) if content else 'None'}")
                        
                        if content:
                            contents.append(content.strip())
                    
                    result = "\n".join(contents) if contents else ""
                    print(f"   ✅ Final result: {repr(result[:200]) if result else 'Empty string'}")
                    
                    # Test the _clean_job_description logic for job_description
                    if field == "job_description" and result:
                        print(f"   🧹 Testing _clean_job_description...")
                        
                        # Simulate the cleaning logic
                        text = result
                        
                        # Handle None or empty text
                        if not text:
                            cleaned = ""
                        else:
                            # Convert to string if not already
                            if not isinstance(text, str):
                                text = str(text)
                            
                            # Only apply unicode_escape if text contains escape sequences
                            try:
                                if '\\' in text:
                                    text = bytes(text, 'utf-8').decode('unicode_escape')
                            except Exception as e:
                                print(f"      ⚠️ Unicode escape decode failed: {e}")
                            
                            # Basic cleaning
                            import re
                            text = re.sub(r'\s+', ' ', text)
                            text = re.sub(r'\n\s*\n', '\n\n', text)
                            cleaned = text.strip()
                        
                        print(f"   🧹 Cleaned result: {repr(cleaned[:200]) if cleaned else 'Empty string'}")
                        print(f"   📊 Cleaned length: {len(cleaned)} characters")
                    
                except Exception as e:
                    print(f"   ❌ Error: {e}")
            
            # Test the CSV parsing logic
            print("\n🔍 Testing CSV selector parsing:")
            print("=" * 50)
            
            # Simulate the JSON parsing from CSV
            csv_content_selectors = '{"title": ".text-wrapper .hero-heading", "job_description": "div.col-lg-8.main-col", "skills": "ul", "location": "not_found", "apply_link": "a.js-apply-now.btn.btn-primary"}'
            
            try:
                parsed_selectors = json.loads(csv_content_selectors)
                print(f"✅ Parsed selectors: {parsed_selectors}")
                
                # Test each parsed selector
                for field, selector in parsed_selectors.items():
                    if selector == "not_found":
                        continue
                    
                    elements = await page.query_selector_all(selector)
                    print(f"   {field} ({selector}): {len(elements)} elements")
                    
            except Exception as e:
                print(f"❌ JSON parsing error: {e}")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(debug_cognizant_extraction())
