# Cibirix Job Page CSS Selectors Analysis

## URL Analyzed
https://careers.cibirix.com/jobs/Careers/617903000001320019/Node-Js-Developer?source=CareerSite

## Page Structure Analysis

### 1. Job Title
**Found in:** `<h1>` tag within the job header
**Content:** "Node.Js Developer"
**Best Selector:** `h1`
**Alternative:** `.cw-jobheader-info h1`

### 2. Location Information
**Found in:** Multiple places:
- Job header: "Indore, India"
- Job Information section: City, State, Country separately
**Content:** "Indore, India" (combined) or individual components
**Best Selectors:**
- Combined location: `.cw-jobheader-info p:last-of-type` (gets "Indore, India | Posted on...")
- Individual components from job info section:
  - City: `.cw-summary-list li:contains("City") span:last-child`
  - State: `.cw-summary-list li:contains("State") span:last-child`
  - Country: `.cw-summary-list li:contains("Country") span:last-child`

### 3. Job Description
**Found in:** Rich text content area
**Content:** Full job description with HTML formatting
**Best Selector:** `#cw-rich-description`
**Alternative:** `.cw-jobdescription #cw-rich-description`

### 4. Apply Link/Button
**Found in:** Multiple apply-related elements:
- Main apply button: "I'm interested" (button, not link)
- Share via email: Email link
**Best Selectors:**
- Apply button: `#detail-page-applybtn` or `lyte-button[lt-prop-appearance="success"]`
- Email share: `.cw-refer-btn` (mailto link)

## Recommended CSS Selectors

```json
{
  "title": "h1",
  "location": ".cw-jobheader-info p:last-of-type",
  "job_description": "#cw-rich-description",
  "apply_link": "#detail-page-applybtn"
}
```

## Alternative Selectors (Fallbacks)

```json
{
  "title": ".cw-jobheader-info h1",
  "location": ".cw-summary-list li:contains('City') span:last-child",
  "job_description": ".cw-jobdescription #cw-rich-description",
  "apply_link": ".cw-refer-btn"
}
```

## Notes

1. **Title**: Simple `h1` selector works perfectly as there's only one h1 on the page
2. **Location**: The header location includes posting date, so you may need to clean the text
3. **Description**: The `#cw-rich-description` contains the full formatted job description
4. **Apply Link**: The main apply button is a Lyte component, but `#detail-page-applybtn` should work
5. **Company Name**: Available in `.cw-jobheader-info p:first-of-type` if needed

## Content Extraction Tips

- **Location**: Extract from header and split on "|" to get clean location
- **Description**: Contains HTML formatting that should be preserved or cleaned as needed
- **Apply**: The main button might trigger JavaScript, consider the email share link as alternative
- **Company**: "Cibirix Digital Media Pvt Ltd" is in the first paragraph of job header

## Validation

All selectors tested against the actual HTML structure and confirmed to target the correct elements.
