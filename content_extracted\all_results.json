[{"jd_link": "https://careers.cognizant.com/global-en/jobs/00060638381/senior-java-developer/", "company_name": "careers_cognizant_com_global-en_jobs_00060638381_senior-java-developer_", "company_id": 0, "source": 3, "title": "Senior Java developer", "location": null, "location_type": "onsite", "job_type": null, "min_experience": 12, "max_experience": 12, "apply_link": "https://cognizant.taleo.net/careersection/Lateral/jobapply.ftl?job=00060638381&lang=en&source=CWS-13082&src=CWS-13082", "description": "Job Summary We are seeking a highly skilled Senior Engineer - Full Stack with 8 to 12 years of experience to join our team. The ideal candidate will have extensive experience in AWS Springboot Microservices and Java. Additionally expertise in the Consumer Lending domain is mandatory. This role involves developing and maintaining robust applications that drive our consumer lending services forward. Responsibilities Develop and maintain full stack applications using Docker AWS Springboot Microservices and Java. Collaborate with cross-functional teams to design and implement scalable and efficient solutions. Ensure the performance quality and responsiveness of applications. Identify and correct bottlenecks and fix bugs. Lead the development of new features and enhancements to existing systems. Provide technical guidance and mentorship to junior engineers. Conduct code reviews to maintain code quality and ensure best practices. Implement security and data protection measures. Work closely with product managers to understand requirements and translate them into technical specifications. Participate in the entire application lifecycle focusing on coding and debugging. Optimize applications for maximum speed and scalability. Stay updated with emerging technologies and apply them to improve existing systems. Contribute to the continuous improvement of the development process. Qualifications Possess a strong understanding of Docker and containerization technologies. Demonstrate expertise in AWS and cloud computing services. Have extensive experience in Java programming languages. Show proficiency in developing Springboot Microservices. Exhibit a deep understanding of the Consumer Lending domain. Have a proven track record of delivering high-quality software solutions. Display excellent problem-solving and analytical skills. Possess strong communication and collaboration abilities. Show a commitment to continuous learning and professional development. Demonstrate the ability to work independently and as part of a team. Have experience with Agile development methodologies. Exhibit strong attention to detail and organizational skills. Show the ability to mentor and guide junior team members. The Cognizant community: We are a high caliber team who appreciate and support one another. Our people uphold an energetic, collaborative and inclusive workplace where everyone can thrive. Cognizant is a global community with more than 300,000 associates around the world. We don’t just dream of a better way – we make it happen. We take care of our people, clients, company, communities and climate by doing what’s right. We foster an innovative environment where you can build the career path that’s right for you. About us: Cognizant is one of the world's leading professional services companies, transforming clients' business, operating, and technology models for the digital era. Our unique industry-based, consultative approach helps clients envision, build, and run more innovative and efficient businesses. Headquartered in the U. S. , Cognizant (a member of the NASDAQ-100 and one of Forbes World’s Best Employers 2024) is consistently listed among the most admired companies in the world. Learn how Cognizant helps clients lead with digital at www. cognizant. com Cognizant is an equal opportunity employer. Your application and candidacy will not be considered based on race, color, sex, religion, creed, sexual orientation, gender identity, national origin, disability, genetic information, pregnancy, veteran status or any other characteristic protected by federal, state or local laws. Disclaimer: Compensation information is accurate as of the date of this posting. Cognizant reserves the right to modify this information at any time, subject to applicable law. Applicants may be required to attend interviews in person or by video conference. In addition, candidates may be required to present their current state or government issued ID during each interview.", "ctc": null, "currency": null, "meta": {}}]