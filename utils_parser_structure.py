import logging
import re
import json
from bs4 import BeautifulSoup, Tag
from config import MIN_CHARS_FOR_MAIN_CONTAINER

logger = logging.getLogger(__name__)

def get_element_attributes(element: Tag):
    if not isinstance(element, Tag): return None
    attrs = {
        "tag_name": element.name,
        "classes": sorted(list(set(element.get('class', [])))),
        "id": element.get('id')
    }
    other_attrs = {}
    priority_attrs = ['role', 'itemscope', 'itemtype', 'itemprop']
    data_js_attrs_prefixes = ('data-', 'js-', 'aria-') 
    for pa in priority_attrs:
        if element.has_attr(pa): other_attrs[pa] = element[pa]
    for k, v_list_or_str in element.attrs.items():
        if k not in ['class', 'id', 'style'] and k not in priority_attrs:
            if k.startswith(data_js_attrs_prefixes) or k in ['name']: 
                v_str = " ".join(v_list_or_str) if isinstance(v_list_or_str, list) else v_list_or_str
                other_attrs[k] = v_str
    if other_attrs: attrs["other_attributes"] = dict(sorted(other_attrs.items()))
    return attrs

def _is_significant_content_holder(element: Tag, min_text_len: int, for_main_container: bool = True):
    if not isinstance(element, Tag): return False
    strict_boilerplate_tags = ['head', 'script', 'style', 'meta', 'link', 'svg', 'path', 'iframe', 'button', 'input', 'select', 'textarea', 'option', 'label', 'noscript']
    if element.name in strict_boilerplate_tags: return False
    if for_main_container and element.name in ['nav', 'header', 'footer', 'aside']: return False
    if for_main_container and element.name == 'form' and len(element.get_text(strip=True)) < min_text_len * 1.5: return False

    text_content = element.get_text(strip=True)
    text_len = len(text_content)
    if text_len >= min_text_len: return True
    
    if element.find(['h1','h2','h3','h4','p','ul','ol','dl','table'], recursive=False): return True 
    if element.find(['h1','h2','h3','h4','p','ul','ol','dl','table']): return True 
    if element.get('id'): return True

    classes = element.get('class', [])
    job_keywords = ['job', 'vacancy', 'posting', 'career', 'advert', 'listing', 'description', 'detail', 'position', 'opening', 'role']
    if any(keyword in cls.lower() for cls in classes for keyword in job_keywords): return True

    if for_main_container:
        generic_content_keywords = ['content', 'main', 'body', 'container', 'wrapper', 'entry', 'article-body', 'page-content']
        if any(keyword in cls.lower() for cls in classes for keyword in generic_content_keywords):
            
            if element.parent and element.parent.name == 'body' and text_len < min_text_len * 1.8: 
                if not any(keyword in cls.lower() for cls in classes for keyword in job_keywords):
                    return False 
            return True
            
    if any(k.startswith(('data-', 'js-', 'aria-')) for k in element.attrs) or \
       element.get('role') or element.get('itemprop') or element.get('itemscope'):
        return True
    return False

def find_main_job_container_and_attributes(html_content: str):
    if not html_content: 
        logger.warning("find_main_job_container: html_content is None or empty.")
        return None, None
    soup = BeautifulSoup(html_content, 'html.parser')
    
    schema_el = soup.find(attrs={"itemtype": lambda x: x and isinstance(x, str) and "JobPosting" in x})
    if schema_el and _is_significant_content_holder(schema_el, MIN_CHARS_FOR_MAIN_CONTAINER, True):
        logger.info("Main container: Schema.org JobPosting."); return schema_el, get_element_attributes(schema_el)
    
    priority_selectors = [
        "[role='main'][aria-label*='job description']", "[role='main'][aria-labelledby*='job-title']", 
        "article[itemprop='description']", "div[data-automation-id='jobDescription']", 
        "section[data-testid='job-details']", "div[class*='jobad']", "[class*='job-ad']", 
        "article[class*='job-posting']", "section[class*='job-posting']", "div[class*='job_post']", "div[class*='job-offer']"
    ]
    for selector in priority_selectors:
        elements = soup.select(selector) 
        for el in elements: 
            if _is_significant_content_holder(el, MIN_CHARS_FOR_MAIN_CONTAINER, True):
                logger.info(f"Main container: Priority selector '{selector}'."); return el, get_element_attributes(el)

    semantic_tags_candidates = []
    for tag_name in ['article', 'main']:
        for el in soup.find_all(tag_name):
            if _is_significant_content_holder(el, MIN_CHARS_FOR_MAIN_CONTAINER, True):
                score = len(el.get_text(strip=True))
                if el.find(['h1','h2','h3'], text=re.compile(r'(job|position|vacancy|career|role)', re.I)): score *= 1.3 
                if el.find(attrs={'class': re.compile(r'(responsibilities|qualifications|description|details|reqs)', re.I)}): score *= 1.2
                if el.find(text=re.compile(r'(apply now|submit application)', re.I)): score *= 1.1
                semantic_tags_candidates.append({'element': el, 'score': score, 'method': f'Semantic <{tag_name}>'})
    
    if semantic_tags_candidates:
        best_semantic_cand_info = max(semantic_tags_candidates, key=lambda x: x['score'])
        el_sem = best_semantic_cand_info['element']
        job_kw_regex = re.compile(r'(jobDescription|jobDetails|vacancyContent|postingBody|jobad|jobContent|job-details-wrapper|job-description-wrapper|job-body|content-details)', re.I)
        inner_container = el_sem.find(['div','section'], class_=job_kw_regex)
        if not inner_container: inner_container = el_sem.find(['div','section'], id=job_kw_regex)
        
        if inner_container and _is_significant_content_holder(inner_container, MIN_CHARS_FOR_MAIN_CONTAINER * 0.5, True) \
           and len(inner_container.get_text(strip=True)) > len(el_sem.get_text(strip=True)) * 0.25: 
            logger.info(f"Main container: Refined <{el_sem.name}> to inner {get_element_attributes(inner_container)}.");
            return inner_container, get_element_attributes(inner_container)
        logger.info(f"Main container: {best_semantic_cand_info['method']} (score: {best_semantic_cand_info['score']:.0f}).")
        return el_sem, get_element_attributes(el_sem)

    common_pattern_selectors = [
        "div[class*='job-details']", "section[class*='job-description']",
        "div[class*='job-listing-details']", "div[class*='job_description']", "div[class*='job_details']",
        "div[class*='vacancy-details']", "div[class*='vacancy-description']",
        "div[id*='job-description']", "div[id*='jobDetails']", "section[id*='job-content']",
        "div.job-content", "div.job-main", "div.job-body",
        "div.entry-content", "div.single-content", "div.page-content", "main.content", "article.content" 
    ]
    for selector in common_pattern_selectors:
        for el in soup.select(selector): 
            if _is_significant_content_holder(el, MIN_CHARS_FOR_MAIN_CONTAINER, True):
                in_boilerplate = False
                for p_idx, p_el in enumerate(el.parents):
                    if p_idx >= 4: break 
                    if p_el.name in ['nav', 'header', 'footer', 'aside']:
                        in_boilerplate = True; break
                    if p_el.name == 'body': break
                if not in_boilerplate:
                    logger.info(f"Main container: Common pattern selector '{selector}'."); return el, get_element_attributes(el)

    logger.debug("Trying 'Content Core' heuristic as fallback.")
    all_potential_blocks = soup.find_all(['div', 'section', 'article', 'main']) 
    candidate_elements_with_scores = []

    for el in all_potential_blocks:
        if not _is_significant_content_holder(el, MIN_CHARS_FOR_MAIN_CONTAINER, for_main_container=True):
            continue
        is_deep_in_boilerplate = False 
        if el.name in ['header', 'nav', 'footer', 'aside']: continue
        if el.parent and el.parent.name in ['header', 'nav', 'footer', 'aside']: continue
        
        text_len = len(el.get_text(strip=True))
        score = float(text_len) / 150.0  
        
        num_headings = len(el.find_all(['h2','h3','h4'], recursive=False)); score += num_headings * 0.75 
        num_paragraphs = len(el.find_all('p', recursive=False)); score += num_paragraphs * 0.15
        num_lists = len(el.find_all(['ul','ol','dl'], recursive=False)); score += num_lists * 0.25
        
        el_id_str = el.get('id', '').lower(); el_classes_str = " ".join(el.get('class', [])).lower()
        job_keywords_strong = ['job-description', 'job-details', 'jobdescription', 'vacancy-details', 'posting-body', 'jobad']
        job_keywords_medium = ['job', 'vacancy', 'posting', 'career', 'advert', 'listing', 'description', 'detail', 'position', 'opening', 'role']
        
        if any(kw in el_id_str or kw in el_classes_str for kw in job_keywords_strong): score *= 2.5
        elif any(kw in el_id_str or kw in el_classes_str for kw in job_keywords_medium): score *= 1.8 

        if el.get('role') in ['main', 'article', 'document']: score += 1.5
        
        generic_wrappers = ['wrapper', 'container', 'page-wrapper', 'content-area', 'main-wrapper', 'middle', 'body-container']
        if any(gw in el_classes_str for gw in generic_wrappers) and not \
           (any(kw in el_id_str or kw in el_classes_str for kw in job_keywords_strong + job_keywords_medium)):
            score *= 0.6 
        if el.parent and el.parent.name == 'body' and el.name == 'div' and score < 4.0 : score *= 0.7 

        candidate_elements_with_scores.append({'element': el, 'score': score, 'method': 'Content Core'})

    if not candidate_elements_with_scores:
        logger.warning("Content Core: No candidate elements after filtering."); return None, None

    best_candidate_info = max(candidate_elements_with_scores, key=lambda x: x['score'])
    
    if best_candidate_info['score'] > 2.0: 
        best_el = best_candidate_info['element']
        logger.info(f"Main container: {best_candidate_info['method']} (tag:{best_el.name},id:{best_el.get('id')},cls:{best_el.get('class',[])},score:{best_candidate_info['score']:.2f}).")
        
        parent_attrs = get_element_attributes(best_el)
        parent_classes_str = " ".join(parent_attrs.get('classes', [])).lower()
        parent_is_generic = any(gw in parent_classes_str for gw in generic_wrappers) and \
                            not any(jw in parent_classes_str for jw in job_keywords_medium + job_keywords_strong)
        parent_text_len = len(best_el.get_text(strip=True))

        if parent_is_generic or parent_text_len > 25000: 
            significant_children = [c for c in best_el.children if isinstance(c, Tag) and _is_significant_content_holder(c, MIN_CHARS_FOR_MAIN_CONTAINER * 0.4, False)]
            best_child, best_child_score = None, -1
            for child in significant_children:
                child_score = 0; child_text_len = len(child.get_text(strip=True))
                if child_text_len < MIN_CHARS_FOR_MAIN_CONTAINER * 0.25: continue 
                
                child_attrs_obj = get_element_attributes(child)
                child_id_str = child_attrs_obj.get('id','').lower() if child_attrs_obj.get('id') else ''
                child_classes_str = " ".join(child_attrs_obj.get('classes',[])).lower()

                if any(kw in child_id_str or kw in child_classes_str for kw in job_keywords_strong): child_score += 6
                elif any(kw in child_id_str or kw in child_classes_str for kw in job_keywords_medium): child_score += 4
                child_score += (child_text_len / parent_text_len) * 5 if parent_text_len > 0 else 0 
                if child.name in ['article','section']: child_score +=1.5 
                if child_text_len > MIN_CHARS_FOR_MAIN_CONTAINER: child_score +=1 

                if child_score > best_child_score: best_child_score, best_child = child_score, child
            
            if best_child and best_child_score > 2.5 and (child_text_len / parent_text_len > 0.2 if parent_text_len > 0 else False):
                 logger.info(f"Refined Content Core pick to child: {get_element_attributes(best_child)} (child score: {best_child_score:.2f})")
                 return best_child, get_element_attributes(best_child)
        return best_el, get_element_attributes(best_el)

    logger.warning(f"Could not reliably identify main job container. Best Content Core score was too low: {best_candidate_info['score']:.2f if candidate_elements_with_scores else 'N/A'}")
    return None, None