#!/usr/bin/env python3
"""
Test the Cognizant CSS selectors to debug extraction issues
"""

import asyncio
import json
from playwright.async_api import async_playwright

async def test_cognizant_selectors():
    """Test the Cognizant selectors directly"""
    
    url = "https://careers.cognizant.com/global-en/jobs/00060638381/senior-java-developer/"
    
    # The selectors from your input.csv
    selectors = {
        "title": ".text-wrapper .hero-heading",
        "job_description": "div.col-lg-8.main-col", 
        "skills": "ul",
        "location": "not_found",
        "apply_link": "a.js-apply-now.btn.btn-primary"
    }
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=False)  # Set to False to see what's happening
        page = await browser.new_page()
        
        try:
            print(f"🔍 Testing URL: {url}")
            await page.goto(url, timeout=30000)
            
            # Wait for page to load
            print("⏳ Waiting for page to load...")
            try:
                await page.wait_for_load_state('domcontentloaded', timeout=10000)
                print("✅ DOM content loaded")
            except:
                print("⚠️ DOM load timeout, continuing anyway...")

            try:
                await page.wait_for_load_state('networkidle', timeout=5000)
                print("✅ Network idle")
            except:
                print("⚠️ Network idle timeout, continuing anyway...")
            
            # Check if page loaded successfully
            title = await page.title()
            print(f"📄 Page title: {title}")
            
            # Test each selector
            print("\n🧪 Testing selectors:")
            print("=" * 50)
            
            for field, selector in selectors.items():
                if selector == "not_found":
                    print(f"❌ {field}: Skipped (marked as not_found)")
                    continue
                    
                print(f"\n🔍 Testing {field}: {selector}")
                
                try:
                    # Check if selector exists
                    elements = await page.query_selector_all(selector)
                    print(f"   Found {len(elements)} elements")
                    
                    if elements:
                        # Get text content from first element
                        first_element = elements[0]
                        text_content = await first_element.text_content()
                        inner_text = await first_element.inner_text()
                        
                        print(f"   ✅ Text content: {text_content[:200] if text_content else 'None'}...")
                        print(f"   ✅ Inner text: {inner_text[:200] if inner_text else 'None'}...")
                        
                        # For job description, get more details
                        if field == "job_description" and text_content:
                            print(f"   📊 Description length: {len(text_content)} characters")
                            
                        # For apply link, get href
                        if field == "apply_link":
                            href = await first_element.get_attribute('href')
                            print(f"   🔗 Apply link href: {href}")
                            
                    else:
                        print(f"   ❌ No elements found for selector: {selector}")
                        
                        # Try alternative selectors
                        print("   🔄 Trying alternative selectors...")
                        
                        if field == "title":
                            alt_selectors = ["h1", ".hero-heading", "[class*='hero']", "[class*='title']"]
                        elif field == "job_description":
                            alt_selectors = [".main-col", "[class*='description']", "[class*='content']", "main"]
                        elif field == "apply_link":
                            alt_selectors = ["[class*='apply']", "a[href*='apply']", ".btn-primary"]
                        else:
                            alt_selectors = []
                            
                        for alt_selector in alt_selectors:
                            try:
                                alt_elements = await page.query_selector_all(alt_selector)
                                if alt_elements:
                                    alt_text = await alt_elements[0].text_content()
                                    print(f"      ✅ Alternative '{alt_selector}': {alt_text[:100] if alt_text else 'No text'}...")
                                    break
                            except:
                                continue
                        
                except Exception as e:
                    print(f"   ❌ Error testing selector: {e}")
            
            # Additional debugging - check page structure
            print("\n🔍 Page structure analysis:")
            print("=" * 50)
            
            # Check for common job page elements
            common_selectors = {
                "All h1 tags": "h1",
                "All h2 tags": "h2", 
                "Elements with 'title' class": "[class*='title']",
                "Elements with 'hero' class": "[class*='hero']",
                "Elements with 'description' class": "[class*='description']",
                "Elements with 'apply' class": "[class*='apply']",
                "All buttons": "button",
                "All links": "a"
            }
            
            for desc, sel in common_selectors.items():
                try:
                    elements = await page.query_selector_all(sel)
                    print(f"   {desc}: {len(elements)} found")
                    if elements and len(elements) <= 5:  # Show details for small counts
                        for i, elem in enumerate(elements[:3]):
                            text = await elem.text_content()
                            print(f"      {i+1}. {text[:80] if text else 'No text'}...")
                except:
                    print(f"   {desc}: Error")
            
            # Save page HTML for manual inspection
            html_content = await page.content()
            with open('cognizant_page_debug.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"\n💾 Page HTML saved to: cognizant_page_debug.html")
            
        except Exception as e:
            print(f"❌ Error: {e}")
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(test_cognizant_selectors())
