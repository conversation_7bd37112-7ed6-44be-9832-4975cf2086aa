#!/usr/bin/env python3
"""
Extract CSS selectors from the Cibirix job page
"""

import asyncio
import re
from playwright.async_api import async_playwright
from bs4 import BeautifulSoup

async def analyze_job_page():
    """Analyze the job page and extract CSS selectors"""
    
    url = "https://www.teracloud.io/jd-business-development"
    
    async with async_playwright() as p:
        browser = await p.chromium.launch(headless=True)
        page = await browser.new_page()
        
        try:
            print(f"Fetching: {url}")
            await page.goto(url, timeout=30000)
            await page.wait_for_load_state('networkidle', timeout=10000)
            
            # Get HTML content
            html_content = await page.content()
            
            # Parse with BeautifulSoup
            soup = BeautifulSoup(html_content, 'html.parser')
            
            print("=== ANALYZING PAGE STRUCTURE ===\n")
            
            # 1. EXTRACT TITLE SELECTOR
            print("1. JOB TITLE:")
            title_candidates = []
            
            # Look for h1 tags
            h1_tags = soup.find_all('h1')
            for h1 in h1_tags:
                if h1.get_text(strip=True):
                    title_candidates.append(('h1', h1.get_text(strip=True)[:100]))
            
            # Look for elements with title-related classes
            title_classes = soup.find_all(attrs={'class': re.compile(r'title|job.*title|position', re.I)})
            for elem in title_classes:
                if elem.get_text(strip=True):
                    classes = ' '.join(elem.get('class', []))
                    title_candidates.append((f'.{classes.replace(" ", ".")}', elem.get_text(strip=True)[:100]))
            
            # Look for elements with title-related IDs
            title_ids = soup.find_all(attrs={'id': re.compile(r'title|job.*title|position', re.I)})
            for elem in title_ids:
                if elem.get_text(strip=True):
                    title_candidates.append((f'#{elem.get("id")}', elem.get_text(strip=True)[:100]))
            
            for selector, text in title_candidates:
                print(f"   {selector}: {text}")
            
            # 2. EXTRACT LOCATION SELECTOR
            print("\n2. LOCATION:")
            location_candidates = []
            
            # Look for elements with location-related classes
            location_classes = soup.find_all(attrs={'class': re.compile(r'location|address|city', re.I)})
            for elem in location_classes:
                if elem.get_text(strip=True):
                    classes = ' '.join(elem.get('class', []))
                    location_candidates.append((f'.{classes.replace(" ", ".")}', elem.get_text(strip=True)[:100]))
            
            # Look for elements with location-related IDs
            location_ids = soup.find_all(attrs={'id': re.compile(r'location|address|city', re.I)})
            for elem in location_ids:
                if elem.get_text(strip=True):
                    location_candidates.append((f'#{elem.get("id")}', elem.get_text(strip=True)[:100]))
            
            for selector, text in location_candidates:
                print(f"   {selector}: {text}")
            
            # 3. EXTRACT DESCRIPTION SELECTOR
            print("\n3. JOB DESCRIPTION:")
            desc_candidates = []
            
            # Look for elements with description-related classes
            desc_classes = soup.find_all(attrs={'class': re.compile(r'description|detail|content|summary|requirement', re.I)})
            for elem in desc_classes:
                text = elem.get_text(strip=True)
                if text and len(text) > 100:  # Substantial content
                    classes = ' '.join(elem.get('class', []))
                    desc_candidates.append((f'.{classes.replace(" ", ".")}', len(text), text[:150]))
            
            # Look for elements with description-related IDs
            desc_ids = soup.find_all(attrs={'id': re.compile(r'description|detail|content|summary|requirement', re.I)})
            for elem in desc_ids:
                text = elem.get_text(strip=True)
                if text and len(text) > 100:
                    desc_candidates.append((f'#{elem.get("id")}', len(text), text[:150]))
            
            # Sort by content length (longer is likely better)
            desc_candidates.sort(key=lambda x: x[1], reverse=True)
            
            for selector, length, text in desc_candidates[:5]:  # Top 5 candidates
                print(f"   {selector} ({length} chars): {text}...")
            
            # 4. EXTRACT APPLY LINK SELECTOR
            print("\n4. APPLY LINK:")
            apply_candidates = []
            
            # Look for links with apply-related text or attributes
            links = soup.find_all('a', href=True)
            for link in links:
                href = link.get('href')
                text = link.get_text(strip=True).lower()
                classes = ' '.join(link.get('class', []))
                
                if any(keyword in text for keyword in ['apply', 'submit', 'join']):
                    selector = f'a[href="{href}"]' if href.startswith('http') else f'a[href*="{href}"]'
                    apply_candidates.append((selector, text, href))
                elif any(keyword in classes.lower() for keyword in ['apply', 'submit', 'btn']):
                    selector = f'.{classes.replace(" ", ".")}' if classes else 'a'
                    apply_candidates.append((selector, text, href))
            
            # Look for buttons with apply-related attributes
            buttons = soup.find_all('button')
            for button in buttons:
                text = button.get_text(strip=True).lower()
                classes = ' '.join(button.get('class', []))
                
                if any(keyword in text for keyword in ['apply', 'submit', 'join']):
                    selector = f'.{classes.replace(" ", ".")}' if classes else 'button'
                    apply_candidates.append((selector, text, 'button'))
            
            for selector, text, href in apply_candidates:
                print(f"   {selector}: '{text}' -> {href}")
            
            # 5. GENERATE FINAL SELECTORS
            print("\n" + "="*50)
            print("RECOMMENDED CSS SELECTORS:")
            print("="*50)
            
            # Best title selector
            if title_candidates:
                best_title = title_candidates[0][0]
                print(f'"title": "{best_title}",')
            
            # Best location selector
            if location_candidates:
                best_location = location_candidates[0][0]
                print(f'"location": "{best_location}",')
            
            # Best description selector
            if desc_candidates:
                best_desc = desc_candidates[0][0]
                print(f'"job_description": "{best_desc}",')
            
            # Best apply link selector
            if apply_candidates:
                best_apply = apply_candidates[0][0]
                print(f'"apply_link": "{best_apply}"')
            
            # Save HTML for manual inspection if needed
            with open('cibirix_job_page.html', 'w', encoding='utf-8') as f:
                f.write(html_content)
            print(f"\nFull HTML saved to: cibirix_job_page.html")
            
        except Exception as e:
            print(f"Error: {e}")
        finally:
            await browser.close()

if __name__ == "__main__":
    asyncio.run(analyze_job_page())
