import logging
import time
import random
import json
import re 
import os 

import config 
from config import (
    URLS_FILE, BASE_REQUEST_DELAY, FINE_GRAINED_DATA_POINTS,
    URL_RESULTS_DIR, 
    DEBUG_SAVE_DIR, 
    LOG_FILE, 
    GOOGLE_API_KEY, OPENAI_API_KEY, OPENROUTER_API_KEY 
)
from utils_web import fetch_page_html, get_domain, configure_proxies
from utils_parser_structure import find_main_job_container_and_attributes 
from utils_llm_structure import extract_fine_grained_data_from_html 

logger = logging.getLogger()
if not logger.hasHandlers(): 
    logger.setLevel(getattr(logging, config.LOG_LEVEL.upper(), logging.INFO))
    log_dir = os.path.dirname(LOG_FILE)
    if log_dir and not os.path.exists(log_dir):
        try: os.makedirs(log_dir)
        except OSError as e_mkdir_log: print(f"ERROR: Could not create log directory {log_dir}: {e_mkdir_log}")
    fh = logging.FileHandler(LOG_FILE, encoding='utf-8', mode='a')
    fh_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')
    fh.setFormatter(fh_formatter); logger.addHandler(fh)
    ch = logging.StreamHandler()
    ch_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    ch.setFormatter(ch_formatter); logger.addHandler(ch)

SAVE_RAW_HTML_CONTENT_DEBUG = False 
SAVE_MAIN_CONTAINER_HTML_DEBUG = False 
DEBUG_URL_SUBSTRING_MAIN = "" 

LLM_PROVIDERS = []
if GOOGLE_API_KEY: LLM_PROVIDERS.append("gemini"); logger.info("Gemini provider enabled.")
if OPENAI_API_KEY: LLM_PROVIDERS.append("openai"); logger.info("OpenAI provider enabled.")
if OPENROUTER_API_KEY: LLM_PROVIDERS.append("openrouter"); logger.info("OpenRouter provider enabled.")

if not LLM_PROVIDERS:
    logger.critical("CRITICAL: No LLM providers configured with API keys. Data extraction will likely fail.")

def get_next_llm_provider(current_llm_index):
    if not LLM_PROVIDERS: return None, current_llm_index 
    provider_to_use = LLM_PROVIDERS[current_llm_index % len(LLM_PROVIDERS)]
    next_index = (current_llm_index + 1) % len(LLM_PROVIDERS)
    return provider_to_use, next_index

def format_llm_item_to_final_output(llm_item) -> str:
    """
    The LLM is now prompted to return a CSS selector string directly or "Not Found".
    This function handles that primary case.
    A minimal fallback for a dictionary structure from LLM (if it deviates) is included.
    """
    if isinstance(llm_item, str):
        cleaned_item = llm_item.strip()
        if cleaned_item.lower() == "not found":
            return "not_found"
        if not cleaned_item: 
            logger.debug("format_llm: LLM item was empty string, interpreting as 'not_found'.")
            return "not_found"
        return cleaned_item 

    if isinstance(llm_item, dict):
        logger.warning(f"format_llm: Received dict from LLM (expected string): {llm_item}. Attempting to extract CSS or simple selector.")
        if "css_selector" in llm_item and isinstance(llm_item["css_selector"], str) and llm_item["css_selector"].strip():
            return llm_item["css_selector"].strip()

        if "tag_name" in llm_item and isinstance(llm_item["tag_name"], str) and llm_item["tag_name"].strip():
            tag = llm_item["tag_name"].strip().lower()
            attrs = llm_item.get("attributes", {})
            if not isinstance(attrs, dict): attrs = {}
            
            class_val = attrs.get("class") 
            if class_val and isinstance(class_val, str) and class_val.strip():
                
                formatted_classes = "." + ".".join(class_val.strip().split())
                return f"{tag if tag not in ['any', '*'] else ''}{formatted_classes}"
            
            id_val = attrs.get("id")
            if id_val and isinstance(id_val, str) and id_val.strip():
                return f"{tag if tag not in ['any', '*'] else ''}#{id_val.strip()}"
            
            if not attrs or all(k not in ['class', 'id'] for k in attrs):
                 logger.debug(f"format_llm (dict fallback): Tag '{tag}' had no class/id in attributes. Returning tag only as selector.")
                 return tag 
            
            logger.debug(f"format_llm (dict fallback): Tag '{tag}' dict with attributes {attrs} but no simple class/id selector formed. Defaulting to 'not_found'.")
            return "not_found" 

    logger.warning(f"format_llm: Unhandled LLM item type ({type(llm_item)}), returning 'not_found': {str(llm_item)[:100]}")
    return "not_found"

def _ensure_dir(directory_path):
    if directory_path and not os.path.exists(directory_path):
        try: os.makedirs(directory_path); logger.info(f"Created directory: {directory_path}")
        except OSError as e: logger.error(f"Could not create directory {directory_path}: {e}"); return False
    return True

def analyze_url_structure(url, domain, llm_provider_for_this_url: str) -> dict:
    logger.info(f"Analyzing URL: {url} (LLM: {llm_provider_for_this_url})")
    url_result = {"url": url, "_llm_provider": llm_provider_for_this_url}
    for dp_key in FINE_GRAINED_DATA_POINTS: url_result[dp_key] = "not_found" 
    is_main_debug_url = bool(DEBUG_URL_SUBSTRING_MAIN and DEBUG_URL_SUBSTRING_MAIN in url)

    html_content = None
    try: html_content = fetch_page_html(url)
    except Exception as e:
        logger.error(f"HTML fetch failed for {url}: {e}", exc_info=True)
        url_result["_debug_error_note"] = f"HTML fetch failed: {str(e)}"; return url_result 
    if not html_content:
        logger.error(f"CRITICAL: fetch_page_html returned NO HTML for {url}.")
        url_result["_debug_error_note"] = "HTML content empty post-fetch."; return url_result

    debug_dir_ready = _ensure_dir(DEBUG_SAVE_DIR) if (SAVE_RAW_HTML_CONTENT_DEBUG or SAVE_MAIN_CONTAINER_HTML_DEBUG) else False
    if SAVE_RAW_HTML_CONTENT_DEBUG and (is_main_debug_url or not DEBUG_URL_SUBSTRING_MAIN) and debug_dir_ready:
        fn_domain = (domain or "unk_domain").replace('.', '_')
        fn_slug = re.sub(r'[^a-zA-Z0-9_-]', '_', url.split('?')[0].split('/')[-1] or 'idx')
        raw_path = os.path.join(DEBUG_SAVE_DIR, f"debug_RAW_HTML_{fn_domain}_{fn_slug}.html")
        try:
            with open(raw_path, "w", encoding="utf-8") as f_raw: f_raw.write(f"<!-- URL: {url} -->\n{html_content}")
            logger.info(f"DEBUG: Saved RAW HTML for {url} to {raw_path}")
        except Exception as e_raw: logger.error(f"DEBUG: Failed to save raw HTML: {e_raw}")

    main_container_el, main_container_attrs = find_main_job_container_and_attributes(html_content) 
    if not main_container_el or not main_container_attrs:
        logger.warning(f"No main container for {url}."); url_result["_debug_error_note"] = "Main container not IDed."; return url_result
    
    logger.info(f"Main container for {url}: {main_container_attrs}")
    main_container_html_str = main_container_el.prettify()

    if SAVE_MAIN_CONTAINER_HTML_DEBUG and (is_main_debug_url or not DEBUG_URL_SUBSTRING_MAIN) and debug_dir_ready:
        fn_domain = (domain or "unk_domain").replace('.', '_')
        fn_slug = re.sub(r'[^a-zA-Z0-9_-]', '_', url.split('?')[0].split('/')[-1] or 'idx')
        mc_path = os.path.join(DEBUG_SAVE_DIR, f"debug_MAIN_CONTAINER_{fn_domain}_{fn_slug}.html")
        try:
            with open(mc_path, "w", encoding="utf-8") as f_mc: 
                f_mc.write(f"<!-- URL: {url} -->\n<!-- Attrs: {json.dumps(main_container_attrs, indent=2)} -->\n\n{main_container_html_str}")
            logger.info(f"DEBUG: Saved Main Container HTML for {url} to {mc_path}")
        except Exception as e_mc: logger.error(f"DEBUG: Failed to save Main Container HTML: {e_mc}")

    llm_data = extract_fine_grained_data_from_html(main_container_html_str, FINE_GRAINED_DATA_POINTS, 
                                                   source_url_for_debug=url, llm_provider=llm_provider_for_this_url)
    
    if not llm_data or (isinstance(llm_data, dict) and "error" in llm_data):
        err = llm_data.get("error", "LLM unknown error") if isinstance(llm_data, dict) else "Malformed LLM response"
        logger.error(f"LLM ({llm_provider_for_this_url}) failed for {url}: {err}")
        url_result["_debug_error_note"] = f"LLM ({llm_provider_for_this_url}) failed: {err}"
    else:
        for dp_key in FINE_GRAINED_DATA_POINTS:
            item = llm_data.get(dp_key) 
            if item is None: url_result[dp_key] = "not_found"; logger.warning(f"DP '{dp_key}' missing from LLM res for {url}.")
            else: url_result[dp_key] = format_llm_item_to_final_output(item)
        logger.info(f"LLM ({llm_provider_for_this_url}) processing complete for {url}.")
    return url_result

def save_url_result_to_file(url, result_data):
    if not _ensure_dir(URL_RESULTS_DIR): logger.error(f"Cannot save for {url}, output dir {URL_RESULTS_DIR} issue."); return False
    sanitized_url = re.sub(r'^https?://(www\.)?', '', url)
    sanitized_filename = re.sub(r'[^a-zA-Z0-9_-]+', '_', sanitized_url)
    if len(sanitized_filename) > 150: sanitized_filename = sanitized_filename[:150]
    filepath = os.path.join(URL_RESULTS_DIR, f"{sanitized_filename}.json")
    try:
        with open(filepath, 'w', encoding='utf-8') as f: json.dump(result_data, f, indent=2, ensure_ascii=False)
        logger.debug(f"Saved result for {url} to {filepath}")
        return True
    except Exception as e: logger.error(f"Error saving result for {url} to {filepath}: {e}", exc_info=True); return False

def main():
    logger.info(f"=== Starting Job Data Extractor (Log Level: {config.LOG_LEVEL}) ===")
    if not LLM_PROVIDERS: logger.critical("No LLM providers configured. Exiting."); return
    if hasattr(config, 'PROXIES_CONFIG') and config.PROXIES_CONFIG: configure_proxies(config.PROXIES_CONFIG)
    else: configure_proxies([]); logger.info("No proxies in config.")
    try:
        with open(URLS_FILE, "r", encoding='utf-8') as f: all_urls = [ln.strip() for ln in f if ln.strip() and not ln.startswith("#")]
        logger.info(f"Loaded {len(all_urls)} URLs from {URLS_FILE}.")
    except FileNotFoundError: logger.critical(f"{URLS_FILE} not found. Exiting."); return
    if not all_urls: logger.info("No URLs to process. Exiting."); return

    llm_idx = 0
    for i, url in enumerate(all_urls):
        logger.info(f"--- Analyzing URL {i+1}/{len(all_urls)}: {url} ---")
        if DEBUG_URL_SUBSTRING_MAIN and DEBUG_URL_SUBSTRING_MAIN not in url:
            if i==0: logger.info(f"DEBUG MODE: Only processing URLs with '{DEBUG_URL_SUBSTRING_MAIN}'.")
            continue 
        domain = get_domain(url) 
        if not domain: 
            logger.error(f"Invalid domain for {url}. Creating error entry.")
            err_res = {"url": url, "_debug_error_note": "Invalid domain"}
            for dpk in FINE_GRAINED_DATA_POINTS: err_res[dpk] = "not_found"
            save_url_result_to_file(url, err_res); continue
        
        temp_sanitized_url_part = re.sub(r'^https?://(www\.)?', '', url)
        temp_sanitized_filename = re.sub(r'[^a-zA-Z0-9_-]+', '_', temp_sanitized_url_part)
        if len(temp_sanitized_filename) > 150: temp_sanitized_filename = temp_sanitized_filename[:150]
        existing_filepath = os.path.join(URL_RESULTS_DIR, f"{temp_sanitized_filename}.json")
        if os.path.exists(existing_filepath) and not (DEBUG_URL_SUBSTRING_MAIN and DEBUG_URL_SUBSTRING_MAIN in url) :
             logger.info(f"Result file for {url} already exists at {existing_filepath}. Skipping.")
             continue
        
        current_llm, llm_idx = get_next_llm_provider(llm_idx)
        if not current_llm: logger.error(f"No LLM for {url}. Skipping."); continue
            
        try:
            res = analyze_url_structure(url, domain, current_llm)
            save_url_result_to_file(url, res)
        except Exception as e:
            logger.critical(f"Unhandled CRITICAL exception for {url}: {e}", exc_info=True)
            crit_fail = {"url": url, "_llm_provider": current_llm, "_debug_error_note": f"CRITICAL exception: {str(e)}"}
            for dpk in FINE_GRAINED_DATA_POINTS: crit_fail[dpk] = "not_found"
            save_url_result_to_file(url, crit_fail)
        
        time.sleep(BASE_REQUEST_DELAY + random.uniform(0, BASE_REQUEST_DELAY * 0.3)) 
    
    logger.info(f"=== All URL processing finished. Results in '{URL_RESULTS_DIR}' ===")

if __name__ == "__main__":
    try: main()
    except KeyboardInterrupt: logger.info("Process interrupted by user.")
    except Exception as e: logger.critical(f"Main execution caught top-level error: {e}", exc_info=True)
    finally: logger.info("Application exiting.")