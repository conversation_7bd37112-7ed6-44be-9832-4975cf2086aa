# Job Description Extraction Module
import asyncio
import csv
import json
import os
import logging
from datetime import datetime
from pathlib import Path
from urllib.parse import urljoin
from typing import  Any
from dataclasses import dataclass
from enum import Enum
import re
from pydantic import BaseModel,Field
import aiofiles
from playwright.async_api import async_playwright, Page

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler("jd_scraper.log"),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

class JobSource(Enum):
    """Enum for job sources"""
    LINKEDIN = 1
    INDEED = 2
    COMPANY_WEBSITE = 3
    OTHER = 4

@dataclass
class ExtractionConfig:
    """Configuration for job extraction"""
    input_csv: str
    output_dir: str
    error_dir: str
    processed_dir: str
    batch_size: int
    timeout: int
    concurrent_scrapes: int

    @classmethod
    def default(cls) -> 'ExtractionConfig':
        """Create default configuration"""
        return cls(
            input_csv="input.csv",  # <-- change here
            output_dir="content_extracted",  # <-- change here
            error_dir="data/error_jds",
            processed_dir="data/processed",
            batch_size=50,
            timeout=60000,  # 60 seconds
            concurrent_scrapes=5
        )

class JobModel(BaseModel):
    jd_link: str = Field(...,description="Job description link")
    company_id: int = Field(...,description="Company ID")
    company_name:str = Field(...,description="Company name")
    content_selectors:dict = Field(...,description="Content selectors")
    company_url: str = Field(...,description="Company URL")
    company_linkedin_url: str|None = Field(None,description="Company LinkedIn URL")
    source: int = JobSource.COMPANY_WEBSITE.value


class JDRecord:
    """Class to hold job description data"""


    # Enhanced regex patterns for job type extraction
    JOB_TYPE_PATTERNS = {
        'full_time': r'\b(?:full[- ]?time|permanent|regular|full[- ]?time[- ]?position|full[- ]?time[- ]?role)\b',
        'part_time': r'\b(?:part[- ]?time|temporary|contract|part[- ]?time[- ]?position|part[- ]?time[- ]?role)\b',
        'contract': r'\b(?:contract|freelance|consultant|contract[- ]?position|contract[- ]?role|contract[- ]?based)\b',
        'internship': r'\b(?:intern|internship|trainee|intern[- ]?position|intern[- ]?role|internship[- ]?program)\b',
        'freelance': r'\b(?:freelance|gig|project[- ]?based|freelance[- ]?position|freelance[- ]?role|independent[- ]?contractor)\b',
        'temporary': r'\b(?:temporary|temp|seasonal|temporary[- ]?position|temporary[- ]?role|temp[- ]?position)\b'
    }

    # Enhanced regex patterns for location type extraction
    LOCATION_TYPE_PATTERNS = {
        'remote': r'\b(?:remote|work[- ]?from[- ]?home|wfh|virtual|remote[- ]?position|remote[- ]?work|work[- ]?remotely)\b',
        'hybrid': r'\b(?:hybrid|flexible[- ]?remote|part[- ]?remote|hybrid[- ]?work|hybrid[- ]?position|part[- ]?time[- ]?remote)\b',
        'onsite': r'\b(?:onsite|in[- ]?office|in[- ]?person|at[- ]?location|office[- ]?based|work[- ]?from[- ]?office)\b',
        'flexible': r'\b(?:flexible|any[- ]?location|location[- ]?flexible|flexible[- ]?location|work[- ]?from[- ]?anywhere)\b'
    }

    def __init__(
        self,
        jd_link: str,
        company_id: int,
        company_name: str,
        source: JobSource = JobSource.COMPANY_WEBSITE
    ):
        self.jd_link = jd_link
        self.company_name = company_name
        self.company_id = company_id
        self.source = source
        #Sometime the application form available in jd page itself
        self.apply_link: str = jd_link
        self.description: str | None = None
        self.title: None | str = None
        self.location: None | str = None
        self.location_type: None | str = None
        self.job_type: None | str = None
        self.min_experience: None | int = None
        self.max_experience: None | int = None
        self.ctc: None | str = None
        self.currency: None | str = None
        self.company_dir: None | str = None
        self.meta: dict[str, Any] = {}

    def to_dict(self) -> dict[str, Any]:
        """Convert to dictionary"""
        return {
            "jd_link": self.jd_link,
            "company_name": self.company_name,
            "company_id": self.company_id,
            "source": self.source.value,
            "title": self.title,
            "location": self.location,
            "location_type": self.location_type,
            "job_type": self.job_type,
            "min_experience": self.min_experience,
            "max_experience": self.max_experience,
            "apply_link": self.apply_link,
            "description": self.description,
            "ctc": self.ctc,
            "currency": self.currency,
            "meta": self.meta
        }

    def process_description(self) -> None:
        """Process job description to extract structured information"""
        if not self.description:
            return

        # Extract experience
        self.min_experience, self.max_experience = self.extract_experience(self.description)

        # Extract job type
        self.job_type = self.extract_job_type(self.description)

        # Extract location type
        self.location_type = self.extract_location_type(self.description)

        # Log the extracted information
        logger.info(f"Extracted from job description: "
                   f"Experience: {self.min_experience}-{self.max_experience} years, "
                   f"Job Type: {self.job_type}, "
                   f"Location Type: {self.location_type}")

    @classmethod
    def extract_experience(cls, text: str) -> tuple[None | int, None | int]:
        """Extract minimum and maximum experience from text"""
        if not text:
            return None, None

        normalized_text = ' '.join(text.lower().split())

        # Initialize min and max experience
        min_exp = None
        max_exp = None

        # Find all numeric values associated with years
        year_mentions = []

        # Pattern for X+ years (minimum requirement)
        plus_matches = re.findall(r'(\d+)\+\s*(?:years?|yrs?)', normalized_text)
        for match in plus_matches:
            year_mentions.append(('min', int(match)))

        # Pattern for range X-Y years
        range_matches = re.findall(r'(\d+)[-–](\d+)\s*(?:years?|yrs?)', normalized_text)
        for min_val, max_val in range_matches:
            year_mentions.append(('range', int(min_val), int(max_val)))

        # Pattern for "must have X years"
        must_have_matches = re.findall(r'must\s*have\s*(\d+)(?:\+)?\s*(?:years?|yrs?)', normalized_text)
        for match in must_have_matches:
            # Check if it's "must have X+" or just "must have X"
            if '+' in normalized_text[normalized_text.find(match):normalized_text.find(match)+10]:
                year_mentions.append(('min', int(match)))
            else:
                year_mentions.append(('exact', int(match)))

        # Pattern for exact X years
        exact_matches = re.findall(r'(?<![0-9+-])(\d+)\s*(?:years?|yrs?)(?!\+)', normalized_text)
        for match in exact_matches:
            year_mentions.append(('exact', int(match)))

        # Pattern for minimum/at least X years
        min_matches = re.findall(r'(?:minimum|min|at\s*least)\s*(\d+)\s*(?:years?|yrs?)', normalized_text)
        for match in min_matches:
            year_mentions.append(('min', int(match)))

        # Process all the year mentions to determine min_exp and max_exp
        for mention in year_mentions:
            if mention[0] == 'min':
                if min_exp is None or mention[1] < min_exp:
                    min_exp = mention[1]
            elif mention[0] == 'exact':
                if min_exp is None or mention[1] < min_exp:
                    min_exp = mention[1]
                if max_exp is None or mention[1] > max_exp:
                    max_exp = mention[1]
            elif mention[0] == 'range':
                if min_exp is None or mention[1] < min_exp:
                    min_exp = mention[1]
                if max_exp is None or mention[2] > max_exp:
                    max_exp = mention[2]

        return min_exp, max_exp

    @classmethod
    def extract_job_type(cls, text: str) -> None | str:
        """Extract job type from text"""
        if not text:
            return None

        text = text.lower()
        # Count matches for each job type
        job_type_matches = {}
        for job_type, pattern in cls.JOB_TYPE_PATTERNS.items():
            matches = len(re.findall(pattern, text))
            if matches > 0:
                job_type_matches[job_type] = matches

        # Return the job type with the most matches
        if job_type_matches:
            return max(job_type_matches.items(), key=lambda x: x[1])[0]
        return None

    @classmethod
    def extract_location_type(cls, text: str) -> None | str:
        """Extract location type from text"""
        if not text:
            return None

        text = text.lower()
        # Count matches for each location type
        location_matches = {}
        for loc_type, pattern in cls.LOCATION_TYPE_PATTERNS.items():
            matches = len(re.findall(pattern, text))
            if matches > 0:
                location_matches[loc_type] = matches

        # Return the location type with the most matches
        if location_matches:
            return max(location_matches.items(), key=lambda x: x[1])[0]
        return None

class JobExtractor:
    """Main class for job extraction"""
    def __init__(self, config: None | ExtractionConfig = None):
        self.config = config or ExtractionConfig.default()
        self._setup_directories()

    def _setup_directories(self) -> None:
        """Create necessary directories"""
        for directory in [self.config.output_dir, self.config.error_dir, self.config.processed_dir]:
            os.makedirs(directory, exist_ok=True)

    async def process_job_batch(self, batch: list[JobModel]) -> list[JDRecord]:
        """Process a batch of jobs concurrently"""
        async with async_playwright() as playwright:
            browser = await playwright.chromium.launch(headless=True)

            job_records:list[JDRecord] = []
            semaphore = asyncio.Semaphore(self.config.concurrent_scrapes)

            async def process_job(jd_link: str, selectors: dict, company_id: int, company_name: str):
                async with semaphore:
                    page = await browser.new_page()
                    try:
                        # Set realistic browser headers to avoid Cloudflare blocking
                        await page.set_extra_http_headers({
                            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,*/*;q=0.8',
                            'Accept-Language': 'en-US,en;q=0.5',
                            'Accept-Encoding': 'gzip, deflate, br',
                            'DNT': '1',
                            'Connection': 'keep-alive',
                            'Upgrade-Insecure-Requests': '1',
                        })
                        await page.set_viewport_size({"width": 1280, "height": 1024})

                        jd = JDRecord(jd_link, company_id, company_name)
                        jd = await self._scrape_job(page, jd, selectors)

                        job_records.append(jd)

                        logger.info(f"Processed: {jd.jd_link} - {'SUCCESS' if jd.apply_link else 'ERROR'}")
                    except Exception as e:
                        logger.error(f"Error processing job {jd_link}: {e}")
                    finally:
                        await page.close()

            tasks = [
                process_job(
                    val.jd_link,
                    val.content_selectors,
                    val.company_id,
                    val.company_name
                ) for val in batch
            ]
            await asyncio.gather(*tasks)
            # Browser cleanup
            await browser.close()
            logger.info(f"Processed {len(batch)} jobs")
            #await self._save_jd_to_json(job_records)

            return job_records

    def _clean_job_description(self, text):
        """Clean and normalize job description text"""
        # Handle None or empty text
        if not text:
            return ""

        # Convert to string if not already
        if not isinstance(text, str):
            text = str(text)

        # If description not encoded properly then decode bytes
        if isinstance(text, bytes):
            text = text.decode('utf-8')

        # Only apply unicode_escape if text contains escape sequences
        try:
            if '\\' in text:
                text = bytes(text, 'utf-8').decode('unicode_escape')
        except Exception as e:
            logger.debug(f"Unicode escape decode failed, using original text: {e}")
        # Remove excess whitespace
        text = re.sub(r'\s+', ' ', text)

        # Clean up newlines that appear in strange places
        text = re.sub(r'\n\s*\n', '\n\n', text)

        # Fix bullet points
        text = re.sub(r'\n\s*-\s*', '\n• ', text)

        # Ensure proper spacing after punctuation
        text = re.sub(r'([.!?])\s*', r'\1 ', text)

        # Normalize section headers
        text = re.sub(r'(What you will be doing|What you will have):\s*', r'\n\n## \1:\n\n', text)

        # Normalize bullet points in lists
        text = re.sub(r'\n\n([A-Za-z])', r'\n\n• \1', text)

        # Add line breaks before bullet points for better readability
        text = re.sub(r'•', '\n•', text)

        # Clean up multiple bullet points in a row
        text = re.sub(r'\n•\s*\n•', '\n•', text)

        # Make sure that there aren't too many consecutive newlines
        text = re.sub(r'\n{3,}', '\n\n', text)

        return text.strip()

    async def _scrape_job(self, page: Page, jd: JDRecord, selectors: dict[str, str]) -> JDRecord:
        """Scrape job details from a page"""
        try:
            await page.goto(jd.jd_link, timeout=self.config.timeout, wait_until='domcontentloaded')

            # Wait for page to fully load and avoid Cloudflare blocks
            try:
                await page.wait_for_load_state('networkidle', timeout=10000)
            except Exception:
                logger.debug("Network idle timeout, continuing...")

            # Additional wait for dynamic content
            await page.wait_for_timeout(2000)

            # Check if we got blocked by Cloudflare
            page_title = await page.title()
            if 'cloudflare' in page_title.lower() or 'attention required' in page_title.lower():
                logger.warning(f"Cloudflare block detected for {jd.jd_link}, title: {page_title}")
                # Try to wait a bit more and reload
                await page.wait_for_timeout(3000)
                try:
                    await page.reload(wait_until='domcontentloaded')
                    await page.wait_for_timeout(2000)
                except Exception:
                    pass

            # Wait for job content to load
            main_selector = selectors.get("job_description") or selectors.get("title")
            if main_selector:
                try:
                    await page.wait_for_selector(main_selector, timeout=10000)
                except Exception:
                    logger.debug(f"Main selector {main_selector} not found, continuing...")
                    pass  # Continue even if wait times out

            # Extract job information
            jd.title = await self._safe_extract(page, selectors.get("title"))
            jd.location = await self._safe_extract(page, selectors.get("location"))
            description = await self._safe_extract(page, selectors.get("job_description"))
            jd.description = self._clean_job_description(description)
            jd.apply_link = await self._get_apply_link(page, selectors.get("apply_link"), jd.jd_link)

            # Extract company_name from page if selector is provided
            company_name_selector = selectors.get("company_name") 
            if company_name_selector:
                extracted_company_name = await self._safe_extract(page, company_name_selector)
                if extracted_company_name:
                    jd.company_name = extracted_company_name.strip()


            # Process the description to extract structured information
            jd.process_description()

            return jd
        except Exception as e:
            logger.error(f"Error scraping job {jd.jd_link}: {e}")
            return jd
 
    @staticmethod
    async def _safe_extract(page: Page, selector: None | str) -> str:
        """Safely extract text content from a selector"""
        if not selector:
            return None

        try:
            elements = await page.query_selector_all(selector)
            if not elements:
                return None

            contents = []
            for element in elements:
                content = await element.text_content()
                if content:
                    contents.append(content.strip())

            return "\n".join(contents) if contents else ""
        except Exception as e:
            logger.warning(f"Error extracting content with selector {selector}: {e}")
            return None

    @staticmethod
    async def _get_apply_link(page: Page, selector: None | str, base_url: str) -> str:
        """Extract apply link from page"""
        if not selector:
            return base_url

        try:
            element = await page.query_selector(selector)
            if element:
                href = await element.get_attribute("href")
                if href:
                    return urljoin(base_url, href)
        except Exception as e:
            logger.warning(f"Error extracting apply link: {e}")

        return base_url

    async def _save_jd_to_json(self, jds: list[JDRecord]) -> None:

        """
        Save scraped job descriptions to a JSON file, one per company.

        If a file already exists for a company, it will be updated with the new job description.
        Otherwise, a new file will be created.

        :param jds: A list of scraped job descriptions
        :return: None
        """
        try:
            for jd in jds:
                file_path = Path(f"{self.config.output_dir }/{self._sanitize_filename(jd.company_name)}.json")
                file_path.parent.mkdir(parents=True, exist_ok=True)

                # if jd available that company then it should be updated
                if file_path.exists():
                    with open(file_path, 'r', encoding='utf-8') as file:
                        existing_jds:list[dict] = json.load(file)
                    existing_jds.append(jd.to_dict())
                else:
                    existing_jds = [jd.to_dict()]
                with open(file_path, 'w', encoding='utf-8') as file:
                        json.dump(existing_jds, file, indent=4)
        except Exception as e:
            logger.error(f"Error saving JDs to file: {e}")

    

    @staticmethod
    def _sanitize_filename(text: str) -> str:
        """Create a safe filename from text"""
        if not text:
            return datetime.now().strftime("%Y%m%d_%H%M%S")

        invalid_chars = '<>:"/\\|?*'
        for char in invalid_chars:
            text = text.replace(char, '_')

        return text[:100].strip()

    @staticmethod
    async def load_jobs_from_csv(csv_path: str) -> list[JobModel]:
        """Load jobs from CSV file asynchronously"""
        jobs: list[JobModel] = []
        try:
            # Read the file content first
            async with aiofiles.open(csv_path, 'r', encoding='utf-8') as file:
                content = await file.read()

            company_name = ""
            company_url = ""
            company_linkedin = ""
            count = 0

            # Process the CSV content
            from io import StringIO
            reader = csv.DictReader(StringIO(content))

            for row in reader:
                if row.get("company_name"):
                    company_name:str = row["company_name"]
                if row.get("company_url"):
                    company_url:str = row["company_url"]
                if row.get("company_linkedin"):
                    company_linkedin:str = row["company_linkedin"]
                jd_link = row.get("job_url")
                if row.get("content_selectors"):
                    content_selectors:dict = row["content_selectors"]
                if not content_selectors or not jd_link:
                    logger.error(f"Missing content selectors or job link for {company_name}")
                    continue

                #TODO: check the company exists and process recruiters fetching
                company_id = row.get("company_id",0)

                if not jd_link or not content_selectors:
                    continue

                try:
                    # Handle potential format issues in the JSON
                    #content_selectors = content_selectors.replace("'", '"')
                    selectors = json.loads(content_selectors)
                    job = JobModel(
                        company_name=company_name,
                        company_id=company_id,
                        jd_link=jd_link,
                        content_selectors=selectors,
                        company_url=company_url,
                        company_linkedin_url=company_linkedin
                    )
                    jobs.append(job)
                    if not count:
                        count += 1
                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error in row for {jd_link}: {e}")
        except Exception as e:
            logger.error(f"Error loading jobs from CSV: {e}")

        return jobs

    async def extract_job_descriptions(self) -> list[JDRecord]:
        """
        Main entry point for processing job links
        * Load jobs from CSV file asynchronously
        * Process jobs in batches
        * Generate statistics
        """

        jobs = await self.load_jobs_from_csv(self.config.input_csv)
        logger.info(f"Loaded {len(jobs)} jobs from CSV")

        total_processed = 0
        all_results: list[JDRecord] = []

        # Process in batches to avoid memory issues
        for i in range(0, len(jobs), self.config.batch_size):
            batch = jobs[i:min(i + self.config.batch_size, len(jobs))]
            logger.info(f"Processing batch {i//self.config.batch_size + 1}/{(len(jobs)-1)//self.config.batch_size + 1} ({len(batch)} jobs)")

            batch_results = await self.process_job_batch(batch)
            all_results.extend(batch_results)
            total_processed += len(batch)

            logger.info(f"Processed {total_processed}/{len(jobs)} jobs")

        # Step 3: Generate statistics
        successful = sum(1 for jd in all_results if not jd.apply_link)
        logger.info(f"Processing complete. Successful: {successful}/{len(all_results)}")

        # Write summary report asynchronously
        report_data = {
            "total": len(all_results),
            "successful": successful,
            "failed": len(all_results) - successful,
            "timestamp": datetime.now().isoformat(),
            "jobs": [jd.to_dict() for jd in all_results]
        }
        logger.info(f"Report data: {report_data}")

        # async with aiofiles.open("jd_scraping_report.json", "w", encoding="utf-8") as f:
        #     await f.write(json.dumps(report_data, indent=2, ensure_ascii=False))

        return all_results

    async def process_job_links(self):
        """Main entry point for processing job links"""
        all_results = await self.extract_job_descriptions()
        await self._save_jd_to_single_json(all_results)  # <-- Add this line
        # await self._save_jd_to_csv(all_results)  # (Optional: comment out if you don't want CSV)
        return all_results

    async def _save_jd_to_single_json(self, jds: list[JDRecord]) -> None:
        """Save all job descriptions to a single JSON file"""
        try:
            output_path = Path(self.config.output_dir) / "all_results.json"
            rows = [jd.to_dict() for jd in jds]
            output_path.parent.mkdir(parents=True, exist_ok=True)
            with open(output_path, "w", encoding="utf-8") as f:
                json.dump(rows, f, indent=2, ensure_ascii=False)
            logger.info(f"Saved all extracted jobs to {output_path}")
        except Exception as e:
            logger.error(f"Error saving all JDs to single JSON: {e}")

async def amain():
    """Main function to run the extractor"""
    config = ExtractionConfig.default()
    extractor = JobExtractor(config)
    results = await extractor.process_job_links()
    logger.info(f"Total JD files collected: {len(results)}")
    return results


def main():
    asyncio.run(amain())

if __name__ == "__main__":
    main()

