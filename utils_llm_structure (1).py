import logging
import json
import re
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type
import time 
import requests 

try:
    from config import (
        GOOGLE_API_KEY, GOOGLE_MODEL_NAME, GOO<PERSON>LE_MAX_OUTPUT_TOKENS, GOO<PERSON><PERSON>_TEMPERATURE,
        OPENAI_API_KEY, OPENAI_MODEL_NAME, OPENAI_MAX_OUTPUT_TOKENS, OPENAI_TEMPERATURE,
        OPENROUTER_API_KEY, OPENROUTER_MODEL_NAME, OPENROUTER_MAX_OUTPUT_TOKENS, OPENROUTER_TEMPERATURE, 
        FINE_GRAINED_DATA_POINTS, MAX_LLM_CALL_ATTEMPTS
    )
except ImportError: 
    GOOGLE_API_KEY, OPENAI_API_KEY, OPENROUTER_API_KEY = None, None, None
    GOOGLE_MODEL_NAME, OPENAI_MODEL_NAME, OPENROUTER_MODEL_NAME = "models/gemini-1.5-flash-latest", "gpt-3.5-turbo", "mistralai/mistral-7b-instruct"
    GOOGLE_MAX_OUTPUT_TOKENS, OPENAI_MAX_OUTPUT_TOKENS, OPENROUTER_MAX_OUTPUT_TOKENS = 3072, 3072, 3072
    GOOGLE_TEMPERATURE, OPENAI_TEMPERATURE, OPENROUTER_TEMPERATURE = 0.1, 0.1, 0.1
    FINE_GRAINED_DATA_POINTS = ["title", "job_summary", "skills", "location", "how_to_apply"] 
    MAX_LLM_CALL_ATTEMPTS = 3
    _temp_logger = logging.getLogger(__name__) 
    _temp_logger.critical("CRITICAL: Failed to import from config.py in utils_llm_structure.py. Using fallbacks.")


logger = logging.getLogger(__name__)

gemini_client = None
openai_client = None

LOG_LLM_RAW_RESPONSE_DEBUG = False 
_google_api_core_exceptions_module = None
try:
    import google.generativeai as genai 
    from google.api_core import exceptions as ga_exceptions 
    _google_api_core_exceptions_module = ga_exceptions
   
except ImportError:
    logger.warning("google.generativeai or google.api_core.exceptions not found. Google Gemini LLM may not function.")
    genai = None 

GEMINI_RETRY_EXCEPTIONS_TUPLE = ()
if _google_api_core_exceptions_module:
    EXCEPTION_NAMES_TO_RETRY_GEMINI = ["DeadlineExceeded", "ServiceUnavailable", "ResourceExhausted", "InternalServerError", "Aborted", "Unknown"]
    GEMINI_RETRY_EXCEPTIONS_TUPLE = tuple(
        getattr(_google_api_core_exceptions_module, e_name) 
        for e_name in EXCEPTION_NAMES_TO_RETRY_GEMINI 
        if hasattr(_google_api_core_exceptions_module, e_name)
    )

_openai_api_error_types = ()
try:
    import openai
    
    _openai_api_error_types = (
        openai.error.APIError, openai.error.Timeout, openai.error.RateLimitError,
        openai.error.APIConnectionError, openai.error.ServiceUnavailableError,
    )
except ImportError:
    logger.warning("openai library not found. Direct OpenAI LLM calls will fail.")
    openai = None 
OPENROUTER_RETRY_EXCEPTIONS_TUPLE = (
    requests.exceptions.Timeout,
    requests.exceptions.ConnectionError,
    requests.exceptions.ChunkedEncodingError, 
) if 'requests' in globals() else () 


def init_gemini_llm() -> bool: 
    global gemini_client, genai 
    if gemini_client is not None: return True 
    if genai is None: 
        logger.error("Cannot initialize Gemini client: google.generativeai module (genai) was not imported successfully.")
        return False
    if not GOOGLE_API_KEY: 
        logger.error("Cannot initialize Gemini client: GOOGLE_API_KEY is not set.")
        return False
    try:
        logger.info(f"Configuring Gemini client...") 
        genai.configure(api_key=GOOGLE_API_KEY)
        gemini_client = genai 
        logger.info("Gemini client configured successfully.")
        return True
    except Exception as e:
        logger.error(f"Failed to configure Gemini client: {e}", exc_info=True)
        gemini_client = None; return False

def init_openai_llm() -> bool:
    global openai_client, openai
    if openai_client is not None: return True 
    if openai is None: 
        logger.error("Cannot initialize OpenAI client: openai module was not imported successfully.")
        return False
    if not OPENAI_API_KEY: 
        logger.error("Cannot initialize OpenAI client: OPENAI_API_KEY is not set.")
        return False
    try:
        logger.info("Configuring OpenAI client...")
        openai.api_key = OPENAI_API_KEY
        openai_client = openai 
        logger.info("OpenAI client configured successfully.")
        return True
    except Exception as e:
        logger.error(f"Failed to configure OpenAI client: {e}", exc_info=True)
        openai_client = None; return False

def _parse_llm_json_response(response_text: str, llm_provider: str, context: str = "LLM") -> dict:
    if not response_text: return {"error": f"Empty response from {llm_provider} for {context}."}
    try:
        match_md = re.search(r"```(?:json)?\s*([\s\S]*?)\s*```", response_text, re.I)
        json_str = ""
        if match_md: json_str = match_md.group(1).strip()
        else:
            start_brace = response_text.find('{'); end_brace = response_text.rfind('}')
            if start_brace != -1 and end_brace != -1 and end_brace > start_brace:
                json_str = response_text[start_brace : end_brace+1].strip()
            else: json_str = response_text.strip()
        if not json_str: return {"error": f"Could not extract JSON-like content from {llm_provider} response.", "raw_response": response_text}
        return json.loads(json_str)
    except json.JSONDecodeError as je:
        logger.error(f"{llm_provider} response not valid JSON for {context}: {je}. Raw: {response_text[:500]}...", exc_info=False) 
        return {"error": f"{llm_provider} response not valid JSON.", "raw_response": response_text}
    except Exception as e: 
        logger.error(f"Unexpected error parsing {llm_provider} response for {context}: {e}. Raw: {response_text[:500]}...", exc_info=True)
        return {"error": f"Unexpected error parsing {llm_provider} response.", "raw_response": response_text}

@retry(stop=stop_after_attempt(MAX_LLM_CALL_ATTEMPTS), wait=wait_exponential(multiplier=1.5, min=2, max=30),
       retry=retry_if_exception_type(GEMINI_RETRY_EXCEPTIONS_TUPLE), reraise=True)
def _call_gemini_api_internal(prompt_text: str, context: str) -> str:
    global gemini_client 
    if not gemini_client and not init_gemini_llm(): 
        raise Exception(f"Gemini client for {context} failed to initialize before API call.")
    if not hasattr(gemini_client, 'GenerativeModel'): 
        raise ImportError(f"Gemini module (gemini_client) is misconfigured or not available for {context}.")
    
    logger.debug(f"Calling Gemini for {context}. Model: {GOOGLE_MODEL_NAME}. Prompt length: {len(prompt_text)} chars.")
    model = gemini_client.GenerativeModel(GOOGLE_MODEL_NAME)
    
    gen_config_to_pass = None
    if hasattr(gemini_client, 'types') and hasattr(gemini_client.types, 'GenerationConfig'):
        gen_config_obj = gemini_client.types.GenerationConfig(
            temperature=GOOGLE_TEMPERATURE, 
            max_output_tokens=GOOGLE_MAX_OUTPUT_TOKENS
        )
        gen_config_to_pass = gen_config_obj
    else: 
        logger.warning("Gemini SDK's GenerationConfig type not found, using dictionary for generation_config.")
        gen_config_to_pass = {
            "temperature": GOOGLE_TEMPERATURE, 
            "max_output_tokens": GOOGLE_MAX_OUTPUT_TOKENS
        }
            
    response = model.generate_content(prompt_text, generation_config=gen_config_to_pass)
    
    if LOG_LLM_RAW_RESPONSE_DEBUG and hasattr(response, 'usage_metadata'): 
        logger.debug(f"Gemini API Usage for {context}: Prompt Tokens={response.usage_metadata.prompt_token_count}, Candidate Tokens={response.usage_metadata.candidates_token_count}, Total Tokens={response.usage_metadata.total_token_count}")

    if response.parts:
        full_text = "".join(p.text for p in response.parts if hasattr(p, 'text'))
        if not full_text.strip() and response.prompt_feedback and response.prompt_feedback.block_reason:
             block_reason_text = response.prompt_feedback.block_reason
             safety_ratings_text = str(response.prompt_feedback.safety_ratings) if response.prompt_feedback.safety_ratings else "N/A"
             raise Exception(f"Gemini API {context} blocked: {block_reason_text}. Safety: {safety_ratings_text}")
        return full_text 
    
    if response.prompt_feedback and response.prompt_feedback.block_reason:
        block_reason_text = response.prompt_feedback.block_reason
        safety_ratings_text = str(response.prompt_feedback.safety_ratings) if response.prompt_feedback.safety_ratings else "N/A"
        raise Exception(f"Gemini API {context} blocked: {block_reason_text}. Safety: {safety_ratings_text}")
        
    logger.error(f"Gemini API {context} response was empty or malformed. Full response object: {response}")
    raise Exception(f"Gemini API {context} response empty/malformed.")


@retry(stop=stop_after_attempt(MAX_LLM_CALL_ATTEMPTS), wait=wait_exponential(multiplier=1.5, min=2, max=30),
       retry=retry_if_exception_type(_openai_api_error_types), reraise=True)
def _call_openai_api_internal(prompt_text: str, context: str) -> str:
    global openai_client
    if not openai_client and not init_openai_llm(): 
        raise Exception(f"OpenAI client for {context} failed to initialize before API call.")
    if not hasattr(openai, "ChatCompletion"): 
        raise NotImplementedError("OpenAI < 1.0.0 ChatCompletion not found or openai library version mismatch.")
    
    logger.debug(f"Calling OpenAI for {context}. Model: {OPENAI_MODEL_NAME}. Prompt length: {len(prompt_text)} chars.")
    response = openai_client.ChatCompletion.create(
        model=OPENAI_MODEL_NAME,
        messages=[{"role": "user", "content": prompt_text}],
        temperature=OPENAI_TEMPERATURE,
        max_tokens=OPENAI_MAX_OUTPUT_TOKENS
    )
    if LOG_LLM_RAW_RESPONSE_DEBUG and response.usage: 
         logger.debug(f"OpenAI API Usage for {context}: {response.usage}")
    return response.choices[0].message.content


def _is_retryable_http_status_for_router(status_code): 
    return status_code in [429, 500, 502, 503, 504]

def retry_if_openrouter_error(exception): 
    if isinstance(exception, requests.exceptions.RequestException): return True
    if isinstance(exception, requests.exceptions.HTTPError): 
        return _is_retryable_http_status_for_router(exception.response.status_code)
    return False

@retry(stop=stop_after_attempt(MAX_LLM_CALL_ATTEMPTS), wait=wait_exponential(multiplier=1.5, min=2, max=30),
       retry=retry_if_openrouter_error, reraise=True) 
def _call_openrouter_api_internal(prompt_text: str, context: str) -> str:
    if not OPENROUTER_API_KEY:
        logger.error("OPENROUTER_API_KEY not configured for OpenRouter call.")
        raise ValueError("OPENROUTER_API_KEY not configured.")
    
    OPENROUTER_ENDPOINT_URL = "https://openrouter.ai/api/v1/chat/completions"

    logger.debug(f"Calling OpenRouter for {context}. Model: {OPENROUTER_MODEL_NAME}. Endpoint: {OPENROUTER_ENDPOINT_URL}. Prompt len: {len(prompt_text)} chars.")
    
    headers = {
        "Authorization": f"Bearer {OPENROUTER_API_KEY}",
        "Content-Type": "application/json",
        "HTTP-Referer": "YOUR_APPLICATION_URL_OR_NAME", 
        "X-Title": "JobStructureAnalyzer" 
    }
    
    payload = {
        "model": OPENROUTER_MODEL_NAME, 
        "messages": [{"role": "user", "content": prompt_text}],
        "max_tokens": OPENROUTER_MAX_OUTPUT_TOKENS,
        "temperature": OPENROUTER_TEMPERATURE,
    }
    
    try:
        response = requests.post(OPENROUTER_ENDPOINT_URL, headers=headers, json=payload, timeout=45) 

        if LOG_LLM_RAW_RESPONSE_DEBUG:
            logger.debug(f"OpenRouter raw response status: {response.status_code}")
            logger.debug(f"OpenRouter raw response content (first 500 chars): {response.text[:500]}")

        response.raise_for_status() 
        
        response_data = response.json()
        generated_text = ""
        if isinstance(response_data, dict) and \
           response_data.get("choices") and isinstance(response_data["choices"], list) and response_data["choices"] and \
           isinstance(response_data["choices"][0], dict) and response_data["choices"][0].get("message") and \
           isinstance(response_data["choices"][0]["message"], dict) and "content" in response_data["choices"][0]["message"]:
            generated_text = response_data["choices"][0]["message"].get("content", "")
        else:
            logger.error(f"Could not find 'choices[0].message.content' in OpenRouter response for {context}. Response data: {str(response_data)[:500]}")
            raise ValueError("OpenRouter response format not recognized for text extraction.")
        
        if not generated_text and isinstance(generated_text, str): 
            logger.warning(f"OpenRouter returned empty generated text for {context}.")

        return generated_text

    except requests.exceptions.HTTPError as http_err:
        logger.error(f"OpenRouter HTTP error for {context}: {http_err} - Status: {http_err.response.status_code} - Response: {http_err.response.text[:500] if http_err.response else 'NoResponse'}")
        raise 
    except requests.exceptions.RequestException as req_err: 
        logger.error(f"OpenRouter Request exception for {context}: {req_err}")
        raise
    except Exception as e: 
        logger.error(f"Unexpected error calling OpenRouter for {context}: {e}", exc_info=True)
        raise


def call_llm_provider(llm_provider: str, prompt_text: str, context: str) -> str:
    """Dispatches the call to the specified LLM provider."""
    logger.info(f"Dispatching to LLM provider: {llm_provider} for context: {context}")
    if llm_provider == "gemini":
        return _call_gemini_api_internal(prompt_text, context)
    elif llm_provider == "openai":
        return _call_openai_api_internal(prompt_text, context)
    elif llm_provider == "openrouter": 
        return _call_openrouter_api_internal(prompt_text, context)
    else:
        logger.error(f"Unknown LLM provider specified: {llm_provider}")
        raise ValueError(f"Unknown LLM provider: {llm_provider}")


def extract_fine_grained_data_from_html(
        main_container_html: str, 
        data_points_to_extract: list, 
        source_url_for_debug: str = "unknown_url",
        llm_provider: str = "gemini" 
    ) -> dict:

    if not main_container_html: return {"error": "No HTML for data extraction."}
    if not data_points_to_extract: return {"error": "No data points specified."}

    MAX_HTML_CHARS = 48000 
    html_snippet = main_container_html[:MAX_HTML_CHARS] if len(main_container_html) > MAX_HTML_CHARS else main_container_html
    if len(main_container_html) > MAX_HTML_CHARS:
        logger.info(f"HTML for {source_url_for_debug} truncated from {len(main_container_html)} to {MAX_HTML_CHARS} chars for LLM prompt.")

    
    prompt = f"""
    You are an expert HTML parsing assistant for job postings.
    The HTML content provided is from the main content area of a job posting page (URL: {source_url_for_debug}).
    Your primary task is to identify the most concise and robust **CSS selector** for each of the following data points.
    The CSS selector MUST accurately reflect classes and IDs as they **ACTUALLY EXIST** in the provided HTML. Do NOT invent or guess class names or IDs.
    If an element has multiple classes (e.g., class="classA classB"), represent this in the selector as `.classA.classB`.
    {json.dumps(data_points_to_extract)}

    General Instructions for CSS Selectors:
    - For each data point, provide a direct CSS selector string as the value in the JSON output.
    - **Accuracy is paramount:** Only use class names and IDs that are explicitly present in the HTML snippet for the targeted element or its relevant parent container.
    - **Container Focus (for summary, skills, responsibilities, etc.):** For data points that represent a block of information (like "job_summary", "skills"), identify the main HTML block (`div`, `section`, `ul`, etc.) that *semantically contains* this information. The selector should target this container block. Avoid drilling down to individual `p` or `li` tags within such blocks UNLESS that single `p` or `li` IS the entire semantic block for that data point.
    - **Element Focus (for singular, specific data):** For data points like "title" or a specific "location" span or "how_to_apply" button/link, target the specific element itself.
    - **Selector Preference Order (use the simplest, most robust option based on ACTUAL HTML):**
        1.  Direct ID if unique and clearly marks the target/container: `tag#uniqueId` or `#uniqueId`.
        2.  Distinctive class names: `tag.actual-class.another-class` or `.actual-class`.
        3.  If the target is a simple tag and is unique/clear in context (especially for "title" if it has no class/id): `h1`, `p`.
        4.  Concise, stable paths from an identifiable ancestor if direct class/ID on the target/container is weak: e.g., `div.job-details-section > h2.section-title`.
        5.  Attribute selectors if they are the best unique identifier: e.g., `button[data-testid="apply-button"]`.
    - Avoid overly long or fragile selectors (e.g., many `>` or `nth-child`) if a simpler alternative exists. `nth-child` is acceptable if needed to distinguish otherwise identical sibling containers.

    Specific Instructions for Data Points:
    -   **"title":** Find the main heading element (usually `h1`, `h2`, or `h3`) that contains the job title. The CSS selector MUST be for **THIS HEADING ELEMENT ITSELF**.
        *   If it has `class="jobHeader titleFont"`, return `h1.jobHeader.titleFont` (assuming it's an h1).
        *   If it has `id="mainPageTitle"`, return `h2#mainPageTitle` (assuming it's an h2).
        *   If it has both, prefer the class-based selector if classes are descriptive: `h1.jobHeader.titleFont`. If classes are generic but ID is specific, `h1#mainPageTitle` is also good.
        *   If it has NEITHER class nor ID but is clearly the main job title (e.g., the most prominent `h1` in the provided HTML snippet), return just the tag: `h1`.
        *   **Do NOT return the text content of the title.** Only the CSS selector string.

    -   **"job_summary":** This is a CRITICAL field. It describes the job.
        *   Identify the **main HTML block or section** (e.g., `div`, `section`, `article`) that contains the overall summary, role description, or introduction.
        *   This section might be explicitly labeled "Job Summary", "Job Description", "Overview", "About the Role", "The Role".
        *   Crucially, it might also be under less direct headings like **"What You'll Be Doing"**, "The Opportunity", "Your Impact", "About this Position", "Job Purpose", "Role Overview", or it could be the first significant block of descriptive paragraphs if no explicit summary heading exists.
        *   The CSS selector should point to this **CONTAINING BLOCK** of this summary information. Example: `div.summary-section-wrapper`, `section#role-description-content`. For the example where "What You'll Be Doing" is an `h3` followed by paragraphs in a `div.role-desc-content`, the selector for "job_summary" should be `div.role-desc-content`.

    -   **"skills":** Identify the primary container (`ul`, `div`, `section`) of the skills list/tags. Selector should target this **container**. Example: `div.skills-list-block`, `ul.technical-skills-list`.

    -   **"location":** Selector for the specific element (`span`, `p`, `div`) or small block directly containing the location text. Example: `span.location-text-value`, `div.job-location-details`.

    -   **"how_to_apply":** Selector for the apply button (`a` or `button`), form, or the distinct section containing application instructions/link. Example: `a.apply-now-button.primary-action`, `button#submit-application-form`, `div.application-instructions`.

    -   **If Not Found:** If a clear selector for the data point (or its container, as appropriate) cannot be confidently identified based on the ACTUAL HTML, use the exact string "Not Found". Do not invent selectors or classes. "job_summary" is nearly always present in some textual form; look very carefully for it.

    Output: A single, valid JSON object.
    -   Keys: The data point names from the list.
    -   Values: A string (which is the CSS selector based on ACTUAL HTML attributes OR "Not Found"). **The value for each key MUST be a string.**

    HTML Content Snippet:
    ```html
    {html_snippet}
    ```
    JSON Output:
    """
    try:
        response_text = call_llm_provider(
            llm_provider,
            prompt,
            context=f"{llm_provider} data extraction for {source_url_for_debug}"
        )
        
        if LOG_LLM_RAW_RESPONSE_DEBUG:
            logger.info(f"DEBUG LLM RAW RESPONSE ({llm_provider}) for {source_url_for_debug} (first 1000 chars):\n{response_text[:1000]}")
        
        parsed = _parse_llm_json_response(response_text, llm_provider, context=f"data for {source_url_for_debug}")
        if "error" in parsed: 
            return parsed 
        if not isinstance(parsed, dict):
            logger.error(f"LLM response ({llm_provider}) for {source_url_for_debug} parsed but was not a dictionary: {type(parsed)}. Raw: {response_text[:500]}")
            return {"error": "LLM response parsed but was not a dictionary.", "raw_response": response_text}

        validated = {}
        all_ok = True 
        for key_dp in data_points_to_extract: 
            if key_dp not in parsed:
                validated[key_dp] = "Not Found"; all_ok = False 
                logger.warning(f"Data Point '{key_dp}' missing in LLM ({llm_provider}) JSON response for {source_url_for_debug}.")
            else:
                item = parsed[key_dp]
                
                if isinstance(item, str):
                    item_stripped = item.strip()
                    if item_stripped.lower() == "not found": 
                        validated[key_dp] = "Not Found" 
                    elif not item_stripped: 
                        validated[key_dp] = "Not Found"; all_ok = False
                        logger.warning(f"LLM ({llm_provider}) returned empty CSS selector string for '{key_dp}' in {source_url_for_debug}.")
                    else:
                        validated[key_dp] = item_stripped 
                elif isinstance(item, dict): 
                    logger.warning(f"LLM ({llm_provider}) returned a dict for '{key_dp}' (expected string value) for {source_url_for_debug}: {item}. Will attempt to format using main_analyzer formatter.")
                    validated[key_dp] = item 
                else: 
                    validated[key_dp] = "Not Found"; all_ok = False
                    logger.warning(f"Malformed/Unexpected structure from LLM ({llm_provider}) for '{key_dp}' in {source_url_for_debug} (expected string): {item}")
        
        if not all_ok: 
            logger.warning(f"One or more issues encountered while processing LLM ({llm_provider}) response items for {source_url_for_debug}. Review logs. Validated data: {validated}")
        return validated
    except Exception as e: 
        logger.error(f"LLM call/processing ({llm_provider}) failed for {source_url_for_debug} (exception in extract_fine_grained_data_from_html): {e}", exc_info=True)
        return {"error": f"LLM call/processing ({llm_provider}) failed for {source_url_for_debug}: {str(e)}"}


if genai: 
    if not init_gemini_llm(): 
        logger.warning("Initial Gemini client initialization attempt FAILED at module load.")
if openai:
    if not init_openai_llm(): 
        logger.warning("Initial OpenAI client initialization attempt FAILED at module load.")
