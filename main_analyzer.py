import logging
import time
import random
import json
import re
import os
import csv
from bs4 import BeautifulSoup

import config
from config import (
    URLS_FILE, BASE_REQUEST_DELAY, FINE_GRAINED_DATA_POINTS,
    URL_RESULTS_DIR,
    DEBUG_SAVE_DIR,
    LOG_FILE,
    GOOGLE_API_KEY, OPENAI_API_KEY, OPENROUTER_API_KEY
)
from utils_web import fetch_page_html, get_domain, configure_proxies
from utils_parser_structure import find_main_job_container_and_attributes
from utils_llm_structure import extract_fine_grained_data_from_html, KNOWN_PATTERNS, match_known_pattern

# --- Logging setup ---
logger = logging.getLogger()
if not logger.hasHandlers():
    logger.setLevel(getattr(logging, config.LOG_LEVEL.upper(), logging.INFO))
    log_dir = os.path.dirname(LOG_FILE)
    if log_dir and not os.path.exists(log_dir):
        try:
            os.makedirs(log_dir)
        except OSError as e_mkdir_log:
            print(f"ERROR: Could not create log directory {log_dir}: {e_mkdir_log}")
    fh = logging.FileHandler(LOG_FILE, encoding='utf-8', mode='a')
    fh_formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(filename)s:%(lineno)d - %(message)s')
    fh.setFormatter(fh_formatter)
    logger.addHandler(fh)
    ch = logging.StreamHandler()
    ch_formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    ch.setFormatter(ch_formatter)
    logger.addHandler(ch)

SAVE_RAW_HTML_CONTENT_DEBUG = False
SAVE_MAIN_CONTAINER_HTML_DEBUG = False
DEBUG_URL_SUBSTRING_MAIN = ""

LLM_PROVIDERS = []
if GOOGLE_API_KEY:
    LLM_PROVIDERS.append("gemini")
    logger.info("Gemini provider enabled.")
if OPENAI_API_KEY:
    LLM_PROVIDERS.append("openai")
    logger.info("OpenAI provider enabled.")
if OPENROUTER_API_KEY:
    LLM_PROVIDERS.append("openrouter")
    logger.info("OpenRouter provider enabled.")

if not LLM_PROVIDERS:
    logger.critical("CRITICAL: No LLM providers configured with API keys. Data extraction will likely fail.")

def get_next_llm_provider(current_llm_index):
    if not LLM_PROVIDERS:
        return None, current_llm_index
    provider_to_use = LLM_PROVIDERS[current_llm_index % len(LLM_PROVIDERS)]
    next_index = (current_llm_index + 1) % len(LLM_PROVIDERS)
    return provider_to_use, next_index

def format_llm_item_to_final_output(llm_item) -> str:
    """
    The LLM is now prompted to return a CSS selector string directly or "Not Found".
    This function handles that primary case.
    A minimal fallback for a dictionary structure from LLM (if it deviates) is included.
    """
    if isinstance(llm_item, str):
        cleaned_item = llm_item.strip()
        if cleaned_item.lower() == "not found":
            return "not_found"
        if not cleaned_item:
            logger.debug("format_llm: LLM item was empty string, interpreting as 'not_found'.")
            return "not_found"
        return cleaned_item

    if isinstance(llm_item, dict):
        logger.warning(f"format_llm: Received dict from LLM (expected string): {llm_item}. Attempting to extract CSS or simple selector.")
        if "css_selector" in llm_item and isinstance(llm_item["css_selector"], str) and llm_item["css_selector"].strip():
            return llm_item["css_selector"].strip()
        if "tag_name" in llm_item and isinstance(llm_item["tag_name"], str) and llm_item["tag_name"].strip():
            tag = llm_item["tag_name"].strip().lower()
            attrs = llm_item.get("attributes", {})
            if not isinstance(attrs, dict):
                attrs = {}
            class_val = attrs.get("class")
            if class_val and isinstance(class_val, str) and class_val.strip():
                formatted_classes = "." + ".".join(class_val.strip().split())
                return f"{tag if tag not in ['any', '*'] else ''}{formatted_classes}"
            id_val = attrs.get("id")
            if id_val and isinstance(id_val, str) and id_val.strip():
                return f"{tag if tag not in ['any', '*'] else ''}#{id_val.strip()}"
            if not attrs or all(k not in ['class', 'id'] for k in attrs):
                logger.debug(f"format_llm (dict fallback): Tag '{tag}' had no class/id in attributes. Returning tag only as selector.")
                return tag
            logger.debug(f"format_llm (dict fallback): Tag '{tag}' dict with attributes {attrs} but no simple class/id selector formed. Defaulting to 'not_found'.")
            return "not_found"
    logger.warning(f"format_llm: Unhandled LLM item type ({type(llm_item)}), returning 'not_found': {str(llm_item)[:100]}")
    return "not_found"

def _ensure_dir(directory_path):
    if directory_path and not os.path.exists(directory_path):
        try:
            os.makedirs(directory_path)
            logger.info(f"Created directory: {directory_path}")
        except OSError as e:
            logger.error(f"Could not create directory {directory_path}: {e}")
            return False
    return True

def analyze_url_structure(url, domain, llm_provider_for_this_url: str) -> dict:
    logger.info(f"Analyzing URL: {url} (LLM: {llm_provider_for_this_url})")
    url_result = {"url": url, "_llm_provider": llm_provider_for_this_url}
    for dp_key in FINE_GRAINED_DATA_POINTS:
        url_result[dp_key] = "not_found"
    is_main_debug_url = bool(DEBUG_URL_SUBSTRING_MAIN and DEBUG_URL_SUBSTRING_MAIN in url)

    html_content = None
    try:
        html_content = fetch_page_html(url)
    except Exception as e:
        logger.error(f"HTML fetch failed for {url}: {e}", exc_info=True)
        url_result["_debug_error_note"] = f"HTML fetch failed: {str(e)}"
        return url_result
    if not html_content:
        logger.error(f"CRITICAL: fetch_page_html returned NO HTML for {url}.")
        url_result["_debug_error_note"] = "HTML content empty post-fetch."
        return url_result

    # Save debug HTML if configured
    debug_dir_ready = _ensure_dir(DEBUG_SAVE_DIR) if (SAVE_RAW_HTML_CONTENT_DEBUG or SAVE_MAIN_CONTAINER_HTML_DEBUG) else False
    if SAVE_RAW_HTML_CONTENT_DEBUG and (is_main_debug_url or not DEBUG_URL_SUBSTRING_MAIN) and debug_dir_ready:
        fn_domain = (domain or "unk_domain").replace('.', '_')
        fn_slug = re.sub(r'[^a-zA-Z0-9_-]', '_', url.split('?')[0].split('/')[-1] or 'idx')
        raw_path = os.path.join(DEBUG_SAVE_DIR, f"debug_RAW_HTML_{fn_domain}_{fn_slug}.html")
        try:
            with open(raw_path, "w", encoding="utf-8") as f_raw:
                f_raw.write(f"<!-- URL: {url} -->\n{html_content}")
            logger.info(f"DEBUG: Saved RAW HTML for {url} to {raw_path}")
        except Exception as e_raw:
            logger.error(f"DEBUG: Failed to save raw HTML: {e_raw}")

    main_container_el, main_container_attrs = find_main_job_container_and_attributes(html_content)
    if not main_container_el or not main_container_attrs:
        logger.warning(f"No main container for {url}.")
        url_result["_debug_error_note"] = "Main container not IDed."
        return url_result

    logger.info(f"Main container for {url}: {main_container_attrs}")
    main_container_html_str = main_container_el.prettify()

    # Save main container HTML if configured
    if SAVE_MAIN_CONTAINER_HTML_DEBUG and (is_main_debug_url or not DEBUG_URL_SUBSTRING_MAIN) and debug_dir_ready:
        fn_domain = (domain or "unk_domain").replace('.', '_')
        fn_slug = re.sub(r'[^a-zA-Z0-9_-]', '_', url.split('?')[0].split('/')[-1] or 'idx')
        mc_path = os.path.join(DEBUG_SAVE_DIR, f"debug_MAIN_CONTAINER_{fn_domain}_{fn_slug}.html")
        try:
            with open(mc_path, "w", encoding="utf-8") as f_mc:
                f_mc.write(f"<!-- URL: {url} -->\n<!-- Attrs: {json.dumps(main_container_attrs, indent=2)} -->\n\n{main_container_html_str}")
            logger.info(f"DEBUG: Saved Main Container HTML for {url} to {mc_path}")
        except Exception as e_mc:
            logger.error(f"DEBUG: Failed to save Main Container HTML: {e_mc}")

    # Get LLM-based selectors
    llm_data = extract_fine_grained_data_from_html(
        main_container_html_str,
        FINE_GRAINED_DATA_POINTS,
        source_url_for_debug=url,
        llm_provider=llm_provider_for_this_url
    )

    if not llm_data or (isinstance(llm_data, dict) and "error" in llm_data):
        err = llm_data.get("error", "LLM unknown error") if isinstance(llm_data, dict) else "Malformed LLM response"
        logger.error(f"LLM ({llm_provider_for_this_url}) failed for {url}: {err}")
        url_result["_debug_error_note"] = f"LLM ({llm_provider_for_this_url}) failed: {err}"
    else:
        # Process and validate each selector from LLM
        for dp_key in FINE_GRAINED_DATA_POINTS:
            item = llm_data.get(dp_key)
            if item is None:
                url_result[dp_key] = "not_found"
                logger.warning(f"DP '{dp_key}' missing from LLM res for {url}.")
            else:
                # Format the LLM response to a CSS selector
                initial_selector = format_llm_item_to_final_output(item)
                
                # Validate and improve the selector
                is_valid, improved_selector = validate_and_improve_css_selector(
                    main_container_html_str, 
                    initial_selector,
                    element_type=dp_key
                ),
                
                if is_valid:
                    url_result[dp_key] = improved_selector
                    if improved_selector != initial_selector:
                        logger.info(f"Improved selector for '{dp_key}': {initial_selector} -> {improved_selector}")
                else:
                    url_result[dp_key] = "not_found"
                    logger.warning(f"Invalid selector for '{dp_key}': {initial_selector}")
                    
                    # Try fallback methods for important fields
                    if dp_key == "title":
                        fallback = find_job_title_class(main_container_html_str)
                        if fallback != "not_found":
                            url_result[dp_key] = fallback
                            logger.info(f"Used fallback for title: {fallback}")
                    elif dp_key == "job_summary":
                        fallback = find_job_description_class(main_container_html_str)
                        if fallback != "not_found":
                            url_result[dp_key] = fallback
                            logger.info(f"Used fallback for job_summary: {fallback}")
        
        logger.info(f"LLM ({llm_provider_for_this_url}) processing complete for {url}.")
    return url_result

def save_url_result_to_file(url, result_data):
    if not _ensure_dir(URL_RESULTS_DIR):
        logger.error(f"Cannot save for {url}, output dir {URL_RESULTS_DIR} issue.")
        return False
    sanitized_url = re.sub(r'^https?://(www\.)?', '', url)
    sanitized_filename = re.sub(r'[^a-zA-Z0-9_-]+', '_', sanitized_url)
    if len(sanitized_filename) > 150:
        sanitized_filename = sanitized_filename[:150]
    filepath = os.path.join(URL_RESULTS_DIR, f"{sanitized_filename}.json")
    try:
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(result_data, f, indent=2, ensure_ascii=False)
        logger.debug(f"Saved result for {url} to {filepath}")
        return True
    except Exception as e:
        logger.error(f"Error saving result for {url} to {filepath}: {e}", exc_info=True)
        return False

def save_results_to_csv(results, csv_filepath):
    # Define your CSV columns
    fieldnames = [
        "company_name",
        "job_url",
        "content_selectors",
        "company_url",
        "company_linkedin"
    ]
    with open(csv_filepath, "w", newline='', encoding="utf-8") as csvfile:
        writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
        writer.writeheader()
        for row in results:
            writer.writerow(row)

def find_job_description_class(html):
    soup = BeautifulSoup(html, "html.parser")
    keywords = ['description', 'job-desc', 'job_description', 'job-content', 'job_content', 'job-details', 'jobdetails']
    candidates = []
    for tag in soup.find_all(['div', 'section', 'article']):
        class_list = tag.get('class', [])
        id_val = tag.get('id', '')
        class_str = ' '.join(class_list)
        if any(kw in class_str.lower() for kw in keywords) or any(kw in id_val.lower() for kw in keywords):
            candidates.append((tag, len(tag.get_text(strip=True))))
    if candidates:
        best = max(candidates, key=lambda x: x[1])[0]
        if best.get('id'):
            return f"#{best['id']}"
        elif best.get('class'):
            return '.' + '.'.join(best['class'])
    return "not_found"

def find_job_title_class(html):
    soup = BeautifulSoup(html, "html.parser")
    title_keywords = ['title', 'job-title', 'position', 'role', 'vacancy', 'opening', 'career-title', 'posting-title', 'job_header', 'jobheader']
    candidates = []
    for tag in soup.find_all(['h1', 'h2', 'h3', 'h4', 'div', 'span']):
        class_list = tag.get('class', [])
        id_val = tag.get('id', '')
        class_str = ' '.join(class_list)
        if any(kw in class_str.lower() for kw in title_keywords) or any(kw in id_val.lower() for kw in title_keywords):
            candidates.append((tag, len(tag.get_text(strip=True))))
    if candidates:
        best = max(candidates, key=lambda x: x[1])[0]
        if best.get('id'):
            return f"#{best['id']}"
        elif best.get('class'):
            return '.' + '.'.join(best['class'])
    for tag in soup.find_all(['h1', 'h2']):
        if tag.get_text(strip=True):
            if tag.get('id'):
                return f"#{tag['id']}"
            elif tag.get('class'):
                return '.' + '.'.join(tag['class'])
            else:
                return tag.name
    return "not_found"

def validate_and_improve_css_selector(html_content, selector, element_type="generic"):
    """
    Validates a CSS selector against HTML content and attempts to improve it if invalid.
    
    Args:
        html_content: HTML string to test against
        selector: CSS selector to validate
        element_type: Type of element we're looking for (title, description, etc.)
    
    Returns:
        Tuple of (is_valid, improved_selector)
    """
    if not selector or selector == "not_found":
        return False, "not_found"
        
    soup = BeautifulSoup(html_content, 'html.parser')
    
    # First check if the selector is valid and matches elements
    try:
        elements = soup.select(selector)
        if elements:
            # If we found elements, check if they're appropriate for the element type
            if element_type == "title" and len(elements) > 3:
                # Too many matches for a title, likely too generic
                pass  # Continue to improvement
            elif element_type == "job_summary" and sum(len(el.get_text(strip=True)) for el in elements) < 100:
                # Description too short, likely wrong selector
                pass  # Continue to improvement
            else:
                # Selector works and seems appropriate
                return True, selector
    except Exception as e:
        logger.debug(f"Invalid selector '{selector}': {str(e)}")
    
    # If we're here, the selector needs improvement
    # Try to fix common issues
    improved = _fix_common_selector_issues(selector)
    if improved != selector:
        try:
            elements = soup.select(improved)
            if elements:
                return True, improved
        except Exception:
            pass
    
    # Try to build alternative selectors based on the element type
    if element_type == "title":
        alternatives = [
            "h1", "h1.job-title", ".job-title", ".position-title", 
            "[data-testid='job-title']", ".job-header h1", ".job-header h2"
        ]
    elif element_type == "job_summary":
        alternatives = [
            ".job-description", "#job-description", ".description", 
            "[data-testid='job-description']", ".job-details", 
            "section.description", "div[class*='description']"
        ]
    elif element_type == "company_name":
        alternatives = [
            ".company-name", "[data-testid='company-name']", 
            ".employer-name", ".organization-name"
        ]
    elif element_type == "location":
        alternatives = [
            ".job-location", ".location", "[data-testid='location']",
            ".job-info .location", "[itemprop='jobLocation']"
        ]
    else:
        alternatives = []
    
    # Try each alternative
    for alt in alternatives:
        try:
            elements = soup.select(alt)
            if elements and sum(len(el.get_text(strip=True)) for el in elements) > 0:
                return True, alt
        except Exception:
            continue
    
    # If all else fails, try to build a selector from the most likely element
    if element_type == "title":
        return _find_best_title_selector(soup)
    elif element_type == "job_summary":
        return _find_best_description_selector(soup)
    
    return False, "not_found"

def _fix_common_selector_issues(selector):
    """Fix common issues with CSS selectors"""
    if not selector:
        return selector
        
    # Remove quotes that might cause issues
    selector = re.sub(r'[\'"]', '', selector)
    
    # Fix missing dots before class names - but be careful not to add dots to tag names
    # This regex is complex, so let's simplify the approach
    parts = []
    for part in selector.split():
        if part and not part.startswith('.') and not part.startswith('#') and not part.startswith('['):
            # This might be a tag name or a class without a dot
            if re.search(r'[^a-zA-Z0-9_-]', part):
                # Contains special chars, so not a simple tag name
                part = '.' + part
        parts.append(part)
    selector = ' '.join(parts)
    
    # Fix spaces in class names (should be dots)
    selector = re.sub(r'\.([^.#\s]+)\s+([^.#\s]+)', r'.\1.\2', selector)
    
    # Remove invalid characters
    selector = re.sub(r'[^\w\s.#\[\]=\-:>+~^$|*]', '', selector)
    
    # Fix doubled class indicators
    selector = re.sub(r'\.\.+', '.', selector)
    
    # Fix doubled ID indicators
    selector = re.sub(r'##', '#', selector)
    
    # Fix spaces around combinators
    selector = re.sub(r'\s*>\s*', ' > ', selector)
    
    # Simplify overly complex selectors (keep only the most specific part)
    if len(selector.split()) > 3:
        parts = selector.split()
        if any('#' in part for part in parts):
            # Keep only the part with ID
            for part in parts:
                if '#' in part:
                    return part
        else:
            # Keep only the last 2-3 parts
            return ' '.join(parts[-3:])
    
    return selector

def _find_best_title_selector(soup):
    """Find the best selector for job title"""
    # Strategy 1: Look for h1 tags
    h1_elements = soup.find_all('h1')
    if h1_elements:
        for h1 in h1_elements:
            text = h1.get_text(strip=True)
            if text and 10 <= len(text) <= 100:  # Reasonable title length
                # Build a selector for this h1
                return True, _build_robust_selector(h1)
    
    # Strategy 2: Look for elements with title-related classes or IDs
    title_patterns = [
        re.compile(r'job[-_]?title', re.I),
        re.compile(r'position[-_]?title', re.I),
        re.compile(r'vacancy[-_]?title', re.I),
        re.compile(r'posting[-_]?title', re.I)
    ]
    
    for pattern in title_patterns:
        # Check classes
        for element in soup.find_all(attrs={"class": pattern}):
            if element.name in ['h1', 'h2', 'h3', 'div', 'span']:
                return True, _build_robust_selector(element)
        
        # Check IDs
        for element in soup.find_all(attrs={"id": pattern}):
            if element.name in ['h1', 'h2', 'h3', 'div', 'span']:
                return True, _build_robust_selector(element)
    
    # Strategy 3: Look for schema.org markup
    schema_title = soup.find(attrs={"itemprop": "title"})
    if schema_title:
        return True, _build_robust_selector(schema_title)
    
    return False, "not_found"

def _find_best_description_selector(soup):
    """Find the best selector for job description"""
    # Strategy 1: Look for common description containers
    desc_patterns = [
        re.compile(r'job[-_]?description', re.I),
        re.compile(r'job[-_]?details', re.I),
        re.compile(r'description', re.I),
        re.compile(r'job[-_]?body', re.I),
        re.compile(r'vacancy[-_]?details', re.I)
    ]
    
    for pattern in desc_patterns:
        # Check classes
        for element in soup.find_all(attrs={"class": pattern}):
            text = element.get_text(strip=True)
            if text and len(text) > 200:  # Reasonable description length
                return True, _build_robust_selector(element)
        
        # Check IDs
        for element in soup.find_all(attrs={"id": pattern}):
            text = element.get_text(strip=True)
            if text and len(text) > 200:
                return True, _build_robust_selector(element)
    
    # Strategy 2: Look for schema.org markup
    schema_desc = soup.find(attrs={"itemprop": "description"})
    if schema_desc:
        return True, _build_robust_selector(schema_desc)
    
    # Strategy 3: Look for the largest text block that's likely a description
    candidates = []
    for tag in soup.find_all(['div', 'section', 'article']):
        if tag.find_all(['ul', 'ol', 'p']) and not tag.find(['header', 'footer', 'nav']):
            text = tag.get_text(strip=True)
            if len(text) > 300:  # Substantial content
                candidates.append((tag, len(text)))
    
    if candidates:
        best_candidate = max(candidates, key=lambda x: x[1])[0]
        return True, _build_robust_selector(best_candidate)
    
    return False, "not_found"

def _build_robust_selector(element):
    """Build a robust CSS selector for an element that balances specificity and resilience"""
    if not element:
        return "not_found"
    
    # If element has an ID, that's the most specific
    if element.get('id'):
        return f"#{element['id']}"
    
    # If element has classes, use them
    classes = element.get('class', [])
    if classes:
        # Filter out very common classes that might be too generic
        filtered_classes = [c for c in classes if len(c) > 2 and c.lower() not in ['row', 'col', 'container', 'wrapper', 'content']]
        if filtered_classes:
            # Use the most specific classes (usually longer names are more specific)
            specific_classes = sorted(filtered_classes, key=len, reverse=True)[:2]
            class_selector = '.'.join(specific_classes)
            return f"{element.name}.{class_selector}"
        
    # If element has data attributes, they can be good identifiers
    data_attrs = {k: v for k, v in element.attrs.items() if k.startswith('data-')}
    if data_attrs:
        # Find the most specific data attribute (prefer shorter values)
        sorted_attrs = sorted(data_attrs.items(), key=lambda x: len(str(x[1])))
        attr_name, attr_value = sorted_attrs[0]
        if attr_value and len(str(attr_value)) < 50:  # Avoid very long attribute values
            return f"{element.name}[{attr_name}='{attr_value}']"
    
    # If we get here, try to build a path-based selector
    # Find a parent with ID or specific classes
    for parent in element.parents:
        if parent.name == 'body':
            break
            
        if parent.get('id'):
            # Found parent with ID, build relative path
            tag_index = _get_element_index(element)
            if tag_index > 0:
                return f"#{parent['id']} > {element.name}:nth-of-type({tag_index})"
            else:
                return f"#{parent['id']} > {element.name}"
                
        parent_classes = parent.get('class', [])
        filtered_parent_classes = [c for c in parent_classes if len(c) > 2 and c.lower() not in ['row', 'col', 'container', 'wrapper', 'content']]
        if filtered_parent_classes:
            specific_class = sorted(filtered_parent_classes, key=len, reverse=True)[0]
            tag_index = _get_element_index(element)
            if tag_index > 0:
                return f".{specific_class} > {element.name}:nth-of-type({tag_index})"
            else:
                return f".{specific_class} > {element.name}"
    
    # Last resort: just use the tag name with nth-child if there are multiple
    tag_index = _get_element_index(element)
    if tag_index > 0:
        return f"{element.name}:nth-of-type({tag_index})"
    
    return element.name

def _get_element_index(element):
    """Get the index of an element among its siblings of the same type"""
    if not element or not element.parent:
        return 0
        
    siblings = element.parent.find_all(element.name, recursive=False)
    if len(siblings) <= 1:
        return 0
        
    for i, sibling in enumerate(siblings, 1):
        if sibling is element:
            return i
            
    return 0

def main():
    logger.info(f"=== Starting Job Data Extractor (Log Level: {config.LOG_LEVEL}) ===")
    if not LLM_PROVIDERS:
        logger.critical("No LLM providers configured. Exiting.")
        return
    if hasattr(config, 'PROXIES_CONFIG') and config.PROXIES_CONFIG:
        configure_proxies(config.PROXIES_CONFIG)
    else:
        configure_proxies([])
        logger.info("No proxies in config.")
    try:
        with open(URLS_FILE, "r", encoding='utf-8') as f:
            all_urls = [ln.strip() for ln in f if ln.strip() and not ln.startswith("#")]
        logger.info(f"Loaded {len(all_urls)} URLs from {URLS_FILE}.")
    except FileNotFoundError:
        logger.critical(f"{URLS_FILE} not found. Exiting.")
        return
    if not all_urls:
        logger.info("No URLs to process. Exiting.")
        return

    logger.info(f"Loaded URLs: {all_urls}")

    llm_idx = 0
    csv_results = []

    for i, url in enumerate(all_urls):
        logger.info(f"--- Analyzing URL {i+1}/{len(all_urls)}: {url} ---")
        if DEBUG_URL_SUBSTRING_MAIN and DEBUG_URL_SUBSTRING_MAIN not in url:
            if i == 0:
                logger.info(f"DEBUG MODE: Only processing URLs with '{DEBUG_URL_SUBSTRING_MAIN}'.")
            continue
        domain = get_domain(url)
        if not domain:
            logger.error(f"Invalid domain for {url}. Creating error entry.")
            err_res = {"url": url, "_debug_error_note": "Invalid domain"}
            for dpk in FINE_GRAINED_DATA_POINTS:
                err_res[dpk] = "not_found"
            continue

        temp_sanitized_url_part = re.sub(r'^https?://(www\.)?', '', url)
        temp_sanitized_filename = re.sub(r'[^a-zA-Z0-9_-]+', '_', temp_sanitized_url_part)
        if len(temp_sanitized_filename) > 150:
            temp_sanitized_filename = temp_sanitized_filename[:150]
        existing_filepath = os.path.join(URL_RESULTS_DIR, f"{temp_sanitized_filename}.json")
        if os.path.exists(existing_filepath) and not (DEBUG_URL_SUBSTRING_MAIN and DEBUG_URL_SUBSTRING_MAIN in url):
            logger.info(f"Result file for {url} already exists at {existing_filepath}. Skipping.")
            continue

        current_llm, llm_idx = get_next_llm_provider(llm_idx)
        if not current_llm:
            logger.error(f"No LLM for {url}. Skipping.")
            continue

        try:
            res = analyze_url_structure(url, domain, current_llm)
            # --- Prepare CSV row ---
            company_name = temp_sanitized_filename  # or extract from domain if you want
            job_url = url
            selectors_dict = {k: res.get(k, "not_found") for k in FINE_GRAINED_DATA_POINTS}
            # Remap job_summary to job_description and how_to_apply to apply_link
            if "job_summary" in selectors_dict:
                selectors_dict["job_description"] = selectors_dict.pop("job_summary")
            if "how_to_apply" in selectors_dict:
                selectors_dict["apply_link"] = selectors_dict.pop("how_to_apply")
            # Pattern matching for selectors
            pattern_match = match_known_pattern(selectors_dict, KNOWN_PATTERNS)
            if pattern_match:
                selectors_dict = pattern_match
            content_selectors = json.dumps(selectors_dict, ensure_ascii=False)
            company_url = f"https://{domain}/"
            company_linkedin = ""  # Fill this if you have a way to get it
            logger.info(f"Appending to CSV: {company_name}, {job_url}")
            csv_results.append({
                "company_name": company_name,
                "job_url": job_url,
                "content_selectors": content_selectors,
                "company_url": company_url,
                "company_linkedin": company_linkedin
            })
        except Exception as e:
            logger.critical(f"Unhandled CRITICAL exception for {url}: {e}", exc_info=True)
            crit_fail = {"url": url, "_llm_provider": current_llm, "_debug_error_note": f"CRITICAL exception: {str(e)}"}
            for dpk in FINE_GRAINED_DATA_POINTS:
                crit_fail[dpk] = "not_found"

        time.sleep(BASE_REQUEST_DELAY + random.uniform(0, BASE_REQUEST_DELAY * 0.3))

    # --- Save all results to CSV at the end ---
    csv_path = os.path.join(URL_RESULTS_DIR, "results.csv")
    save_results_to_csv(csv_results, csv_path)
    logger.info(f"=== All URL processing finished. Results in '{URL_RESULTS_DIR}' and CSV saved to '{csv_path}' ===")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        logger.info("Process interrupted by user.")
    except Exception as e:
        logger.critical(f"Main execution caught top-level error: {e}", exc_info=True)
    finally:
        logger.info("Application exiting.")
